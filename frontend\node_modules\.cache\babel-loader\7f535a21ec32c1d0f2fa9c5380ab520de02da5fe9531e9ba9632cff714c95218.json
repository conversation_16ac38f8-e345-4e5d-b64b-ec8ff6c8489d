{"ast": null, "code": "import { recurringReservationApi, reservationApi } from '@/api';\nimport { isReservationExpired } from '@/utils/date';\nexport default {\n  name: 'RecurringReservationDetail',\n  data() {\n    return {\n      recurringReservationId: null,\n      recurringReservation: null,\n      childReservations: [],\n      loading: true,\n      childReservationsLoading: false,\n      error: false,\n      errorMessage: '',\n      includePastReservations: true,\n      // 默认选中\"包含已过期预约\"\n      cancelDialogVisible: false,\n      userEmail: '',\n      cancelLoading: false,\n      fromChildReservation: false,\n      highlightReservationNumber: null,\n      // 用于高亮显示特定的子预约\n      // 响应式布局相关\n      isMobile: window.innerWidth <= 768\n    };\n  },\n  props: {\n    id: {\n      type: [String, Number],\n      required: false\n    }\n  },\n  created() {\n    // 优先使用props中的id，如果没有则使用路由参数\n    this.recurringReservationId = this.id || this.$route.params.id;\n    console.log('Recurring reservation ID:', this.recurringReservationId);\n\n    // 检查是否是从子预约详情页面返回的\n    this.fromChildReservation = this.$route.query.fromChild === 'true';\n\n    // 检查是否有预约序号参数，用于高亮显示特定的子预约\n    if (this.$route.query.reservation_number) {\n      this.highlightReservationNumber = this.$route.query.reservation_number;\n      console.log('高亮显示预约序号:', this.highlightReservationNumber);\n    }\n\n    // 添加窗口大小变化的监听器\n    window.addEventListener('resize', this.handleResize);\n    this.loadRecurringReservation();\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化的监听器\n    window.removeEventListener('resize', this.handleResize);\n  },\n  // 添加activated钩子函数，在组件被激活时调用（如从子预约详情页面返回）\n  activated() {\n    // 检查子预约状态是否发生变化\n    this.checkChildReservationUpdates();\n  },\n  methods: {\n    // 处理窗口大小变化\n    handleResize() {\n      this.isMobile = window.innerWidth <= 768;\n    },\n    // 加载循环预约详情\n    async loadRecurringReservation() {\n      this.loading = true;\n      this.error = false;\n      try {\n        const response = await recurringReservationApi.getRecurringReservation(this.recurringReservationId);\n        if (response.data.success) {\n          this.recurringReservation = response.data.data;\n          this.userEmail = this.recurringReservation.user_email || '';\n          this.loadChildReservations();\n        } else {\n          this.error = true;\n          this.errorMessage = response.data.message || this.$t('reservation.failedToLoadReservation');\n        }\n      } catch (error) {\n        console.error('Failed to load recurring reservation:', error);\n        this.error = true;\n        this.errorMessage = error.response?.data?.detail || this.$t('reservation.failedToLoadReservation');\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 加载子预约列表\n    async loadChildReservations() {\n      this.childReservationsLoading = true;\n      try {\n        // 始终包含已过期的预约，设置include_past=1\n        const response = await recurringReservationApi.getChildReservations(this.recurringReservationId, 1 // 始终设置为1，表示包含已过期的预约\n        );\n        if (response.data.success) {\n          // 获取所有子预约\n          this.childReservations = response.data.reservations || [];\n\n          // 如果includePastReservations为false，则在前端过滤掉已过期的预约\n          if (!this.includePastReservations) {\n            const now = new Date();\n            this.childReservations = this.childReservations.filter(reservation => {\n              // 保留未过期的预约和已取消的预约\n              const endTime = new Date(reservation.end_datetime);\n              return endTime >= now || reservation.status === 'cancelled';\n            });\n          }\n\n          // 按预约序号排序\n          this.childReservations.sort((a, b) => {\n            // 从预约序号中提取数字部分进行比较\n            const numA = a.reservation_number ? parseInt(a.reservation_number.replace(/\\D/g, '')) : 0;\n            const numB = b.reservation_number ? parseInt(b.reservation_number.replace(/\\D/g, '')) : 0;\n            return numA - numB;\n          });\n          console.log('Child reservations loaded:', this.childReservations.length);\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.failedToLoadChildReservations'));\n        }\n      } catch (error) {\n        console.error('Failed to load child reservations:', error);\n        this.$message.error(this.$t('reservation.failedToLoadChildReservations'));\n      } finally {\n        this.childReservationsLoading = false;\n      }\n    },\n    // 返回上一页\n    goBack() {\n      // 检查是否有来源页面参数\n      const fromAdmin = this.$route.query.fromAdmin === 'true';\n      const reservationCode = this.$route.query.reservationCode;\n\n      // 设置一个标记，表示这个页面是从循环预约详情页面返回的\n      if (fromAdmin) {\n        localStorage.setItem('returning_from_recurring', 'true');\n      }\n      if (fromAdmin && reservationCode) {\n        // 如果是从管理员预约详情页面来的，返回到该页面，并传递fromRecurring参数\n        this.$router.push({\n          path: `/admin/reservation/${reservationCode}`,\n          query: {\n            fromRecurring: 'true'\n          }\n        });\n      } else if (fromAdmin) {\n        // 如果只知道是从管理员页面来的，但没有具体预约码，返回到管理员预约列表\n        this.$router.push('/admin/reservation');\n      } else {\n        // 默认返回到个人预约管理页面\n        this.$router.push('/reservation/query');\n      }\n\n      // 设置一个定时器，在一段时间后清除标记\n      setTimeout(() => {\n        localStorage.removeItem('returning_from_recurring');\n      }, 2000);\n    },\n    // 查看子预约详情\n    viewChildReservation(reservation) {\n      // 检查是否有预约序号\n      if (reservation.reservation_number) {\n        console.log('通过预约序号查看子预约详情:', reservation.reservation_number);\n\n        // 使用预约序号直接跳转到预约详情页面\n        const reservationNumber = reservation.reservation_number;\n        const reservationCode = reservation.reservation_code;\n        console.log('准备跳转到预约详情页面:', {\n          reservationNumber,\n          reservationCode\n        });\n\n        // 构建查询参数\n        const query = {\n          child: 'true',\n          recurringId: this.recurringReservation.id,\n          code: reservationCode // 添加预约码作为查询参数\n        };\n\n        // 保留用户联系方式参数（如果有）\n        if (this.$route.query.userContact) {\n          console.log('viewChildReservation - 保留用户联系方式参数:', this.$route.query.userContact);\n          query.userContact = this.$route.query.userContact;\n        }\n\n        // 保留来源参数（如果有）\n        if (this.$route.query.from) {\n          console.log('viewChildReservation - 保留来源参数:', this.$route.query.from);\n          query.from = this.$route.query.from;\n        }\n        console.log('viewChildReservation - 跳转到子预约详情页面，参数:', query);\n        this.$router.push({\n          path: `/reservation/number/${reservationNumber}`,\n          query: query\n        });\n\n        // 将预约序号和预约码保存到localStorage，以便在页面刷新后仍然可以使用\n        localStorage.setItem('current_reservation_number', reservation.reservation_number);\n        localStorage.setItem('current_reservation_code', reservation.reservation_code);\n        console.log('查看子预约详情:', {\n          id: reservation.id,\n          reservation_code: reservation.reservation_code,\n          reservation_number: reservation.reservation_number,\n          startTime: reservation.start_datetime,\n          endTime: reservation.end_datetime\n        });\n      } else if (reservation.id) {\n        // 如果没有预约序号但有ID，则使用旧方法\n        // 构建查询参数\n        const query = {\n          child: 'true',\n          id: reservation.id,\n          recurringId: this.recurringReservation.id,\n          startTime: reservation.start_datetime,\n          endTime: reservation.end_datetime\n        };\n\n        // 保留用户联系方式参数（如果有）\n        if (this.$route.query.userContact) {\n          console.log('viewChildReservation(旧方法) - 保留用户联系方式参数:', this.$route.query.userContact);\n          query.userContact = this.$route.query.userContact;\n        }\n\n        // 保留来源参数（如果有）\n        if (this.$route.query.from) {\n          console.log('viewChildReservation(旧方法) - 保留来源参数:', this.$route.query.from);\n          query.from = this.$route.query.from;\n        }\n        console.log('viewChildReservation(旧方法) - 跳转到子预约详情页面，参数:', query);\n        this.$router.push({\n          path: `/reservation/${reservation.reservation_code}`,\n          query: query\n        });\n        console.log('使用旧方法查看子预约详情:', {\n          id: reservation.id,\n          reservation_code: reservation.reservation_code,\n          startTime: reservation.start_datetime,\n          endTime: reservation.end_datetime\n        });\n      } else {\n        this.$message.warning('无法查看子预约详情，缺少预约序号和ID');\n      }\n    },\n    // 修改子预约\n    editChildReservation(reservation) {\n      // 检查是否有预约序号\n      if (reservation.reservation_number) {\n        console.log('修改子预约:', reservation.reservation_number);\n\n        // 构建查询参数，直接跳转到预约详情页面并进入编辑模式\n        const query = {\n          edit: 'true',\n          // 添加编辑标记，让预约详情页面直接进入编辑模式\n          from: 'recurring' // 标记来源是循环预约\n        };\n\n        // 保留用户联系方式参数（如果有）\n        if (this.$route.query.userContact) {\n          query.userContact = this.$route.query.userContact;\n        }\n        console.log('editChildReservation - 跳转到子预约详情页面（编辑模式），参数:', query);\n\n        // 跳转到预约详情页面，使用预约序号\n        this.$router.push({\n          path: `/reservation/detail/${reservation.reservation_number}`,\n          query: query\n        });\n      } else {\n        this.$message.warning('无法修改子预约，缺少预约序号');\n      }\n    },\n    // 检查预约是否已开始\n    isReservationStarted(reservation) {\n      if (!reservation || !reservation.start_datetime) return false;\n      const now = new Date();\n      const startTime = new Date(reservation.start_datetime);\n      return now >= startTime;\n    },\n    // 取消子预约\n    async cancelChildReservation(reservation) {\n      try {\n        console.log('准备取消子预约:', reservation);\n        console.log('子预约序号:', reservation.reservation_number);\n        const result = await this.$confirm(this.$t('reservation.cancelConfirmation'), this.$t('common.warning'), {\n          confirmButtonText: this.$t('common.confirm'),\n          cancelButtonText: this.$t('common.cancel'),\n          type: 'warning'\n        });\n        if (result === 'confirm') {\n          // 添加预约序号参数，确保只取消特定的子预约\n          const requestData = {\n            user_email: this.recurringReservation.user_email\n          };\n\n          // 添加预约序号参数，确保只取消特定的子预约\n          if (reservation.reservation_number) {\n            requestData.reservation_number = reservation.reservation_number;\n            console.log('预约序号参数存在:', reservation.reservation_number);\n          } else {\n            console.warn('预约序号参数不存在，将取消所有具有相同预约码的预约');\n          }\n          console.log('取消子预约请求参数:', requestData);\n          console.log('预约码:', reservation.reservation_code);\n          const response = await reservationApi.cancelReservation(reservation.reservation_code, requestData);\n          console.log('取消子预约响应:', response.data);\n          if (response.data.success) {\n            this.$message.success(this.$t('reservation.cancelSuccess'));\n\n            // 直接更新子预约状态为已取消\n            reservation.status = 'cancelled';\n\n            // 保存状态变更到localStorage\n            this.saveChildReservationStatus(reservation, 'cancelled');\n\n            // 强制刷新整个页面，确保获取最新数据\n            console.log('子预约已取消，即将刷新页面...');\n            setTimeout(() => {\n              window.location.reload(true);\n            }, 1000);\n          } else {\n            this.$message.error(response.data.message || this.$t('reservation.cancelFailed'));\n          }\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('Failed to cancel reservation:', error);\n          this.$message.error(this.$t('reservation.cancelFailed'));\n        }\n      }\n    },\n    // 保存子预约状态到localStorage\n    saveChildReservationStatus(reservation, status) {\n      if (!reservation) return;\n\n      // 使用预约码作为键\n      const stateKey = `reservation_status_${reservation.reservation_code}`;\n\n      // 保存状态信息\n      const state = {\n        statusText: this.getChildStatusText({\n          ...reservation,\n          status: status\n        }),\n        statusType: this.getChildStatusType({\n          ...reservation,\n          status: status\n        }),\n        dbStatus: status,\n        forcedStatus: status,\n        timestamp: new Date().getTime(),\n        permanent: true,\n        reservationCode: reservation.reservation_code\n      };\n      console.log('保存子预约状态到localStorage:', state);\n      localStorage.setItem(stateKey, JSON.stringify(state));\n    },\n    // 检查子预约状态是否发生变化\n    checkChildReservationUpdates() {\n      console.log('检查子预约状态是否发生变化');\n\n      // 检查是否有子预约状态变更标记\n      const recurringStateKey = `recurring_reservation_${this.recurringReservationId}_child_status_changed`;\n      const recurringStateStr = localStorage.getItem(recurringStateKey);\n      if (recurringStateStr) {\n        try {\n          const recurringState = JSON.parse(recurringStateStr);\n\n          // 检查状态变更是否还是新鲜的（5分钟内）\n          const now = new Date().getTime();\n          const fiveMinutes = 5 * 60 * 1000;\n          if (now - recurringState.timestamp <= fiveMinutes) {\n            console.log('检测到子预约状态变更，刷新列表:', recurringState);\n\n            // 移除状态变更标记\n            localStorage.removeItem(recurringStateKey);\n\n            // 重新加载子预约列表\n            this.loadChildReservations();\n            return;\n          } else {\n            // 如果状态变更过期，移除它\n            localStorage.removeItem(recurringStateKey);\n          }\n        } catch (e) {\n          console.error('解析子预约状态变更标记时出错:', e);\n        }\n      }\n\n      // 如果没有子预约状态变更标记，检查每个子预约的状态\n      if (this.childReservations.length > 0) {\n        let needRefresh = false;\n\n        // 检查每个子预约的状态\n        for (let i = 0; i < this.childReservations.length; i++) {\n          const reservation = this.childReservations[i];\n          const stateKey = `reservation_status_${reservation.reservation_code}`;\n          const savedStateStr = localStorage.getItem(stateKey);\n          if (savedStateStr) {\n            try {\n              const savedState = JSON.parse(savedStateStr);\n\n              // 检查保存的状态是否还是新鲜的（5分钟内）\n              const now = new Date().getTime();\n              const fiveMinutes = 5 * 60 * 1000;\n              if (now - savedState.timestamp <= fiveMinutes) {\n                console.log(`检测到子预约 ${reservation.reservation_code} 的状态可能已更改，保存的状态:`, savedState);\n\n                // 如果状态已变更为已取消，需要刷新子预约列表\n                if (savedState.forcedStatus === 'cancelled' || savedState.statusText === this.$t('reservation.cancelled') && savedState.statusType === 'danger') {\n                  console.log(`子预约 ${reservation.reservation_code} 已被标记为已取消，需要刷新列表`);\n                  needRefresh = true;\n                  break;\n                }\n              } else {\n                // 如果状态过期，则移除它\n                localStorage.removeItem(stateKey);\n              }\n            } catch (e) {\n              console.error(`解析子预约 ${reservation.reservation_code} 的保存状态时出错:`, e);\n            }\n          }\n        }\n\n        // 如果需要刷新，重新加载子预约列表\n        if (needRefresh) {\n          console.log('检测到子预约状态变更，刷新列表');\n          this.loadChildReservations();\n        }\n      }\n    },\n    // 显示取消循环预约对话框\n    showCancelDialog() {\n      this.cancelDialogVisible = true;\n    },\n    // 确认取消循环预约\n    async confirmCancel() {\n      this.cancelLoading = true;\n      try {\n        const response = await recurringReservationApi.cancelRecurringReservation(this.recurringReservationId, this.userEmail || null);\n        if (response.data.success) {\n          this.$message.success(this.$t('reservation.cancelSuccess'));\n          this.cancelDialogVisible = false;\n          this.loadRecurringReservation();\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.cancelFailed'));\n        }\n      } catch (error) {\n        console.error('Failed to cancel recurring reservation:', error);\n        this.$message.error(this.$t('reservation.cancelFailed'));\n      } finally {\n        this.cancelLoading = false;\n      }\n    },\n    // 格式化日期\n    formatDate(dateStr) {\n      if (!dateStr) return '';\n      const date = new Date(dateStr);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\n    },\n    // 格式化时间\n    formatTime(timeStr) {\n      if (!timeStr) return '';\n      return timeStr.substring(0, 5);\n    },\n    // 格式化日期时间\n    formatDateTime(row, column, cellValue) {\n      if (!cellValue) return '';\n      const date = new Date(cellValue);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;\n    },\n    // 格式化星期几\n    formatDaysOfWeek(days) {\n      if (!days || days.length === 0) return '';\n      const dayNames = [this.$t('reservation.sunday'), this.$t('reservation.monday'), this.$t('reservation.tuesday'), this.$t('reservation.wednesday'), this.$t('reservation.thursday'), this.$t('reservation.friday'), this.$t('reservation.saturday')];\n      return days.map(day => dayNames[day]).join(', ');\n    },\n    // 格式化每月几号\n    formatDaysOfMonth(days) {\n      if (!days || days.length === 0) return '';\n      return days.join(', ');\n    },\n    // 获取循环模式文本\n    getPatternText(pattern) {\n      const patterns = {\n        'daily': this.$t('reservation.daily'),\n        'weekly': this.$t('reservation.weekly'),\n        'monthly': this.$t('reservation.monthly'),\n        'custom': this.$t('reservation.custom')\n      };\n      return patterns[pattern] || pattern;\n    },\n    // 获取循环预约状态类型\n    getStatusType(reservation) {\n      if (reservation.status === 'cancelled') {\n        return 'danger';\n      }\n      return 'success';\n    },\n    // 获取循环预约状态文本\n    getStatusText(reservation) {\n      if (reservation.status === 'cancelled') {\n        return this.$t('reservation.cancelled');\n      }\n      return this.$t('reservation.active');\n    },\n    // 获取子预约状态类型\n    getChildStatusType(reservation) {\n      // 如果预约已取消，返回红色\n      if (reservation.status === 'cancelled') {\n        return 'danger';\n      }\n\n      // 如果预约已过期，返回橙色\n      if (isReservationExpired(reservation.end_datetime)) {\n        return 'warning';\n      }\n\n      // 如果预约正在进行中，返回蓝色\n      const now = new Date();\n      const start = new Date(reservation.start_datetime);\n      const end = new Date(reservation.end_datetime);\n      if (now >= start && now <= end) {\n        return 'primary';\n      }\n\n      // 如果预约已确认且未开始，返回绿色\n      return 'success';\n    },\n    // 获取子预约状态文本\n    getChildStatusText(reservation) {\n      // 如果预约已取消，显示\"已取消\"\n      if (reservation.status === 'cancelled') {\n        return this.$t('reservation.cancelled');\n      }\n\n      // 如果预约已过期，显示\"已过期\"\n      if (isReservationExpired(reservation.end_datetime)) {\n        return this.$t('reservation.expired');\n      }\n\n      // 如果预约正在进行中，显示\"进行中\"\n      const now = new Date();\n      const start = new Date(reservation.start_datetime);\n      const end = new Date(reservation.end_datetime);\n      if (now >= start && now <= end) {\n        return this.$t('reservation.ongoing');\n      }\n\n      // 如果预约已确认且未开始，显示\"已确认\"\n      return this.$t('reservation.confirmed');\n    },\n    // 获取行的类名，用于高亮显示特定的子预约\n    getRowClassName({\n      row\n    }) {\n      if (this.highlightReservationNumber && row.reservation_number === this.highlightReservationNumber) {\n        return 'highlighted-row';\n      }\n      return '';\n    }\n  }\n};", "map": {"version": 3, "names": ["recurringReservationApi", "reservationApi", "isReservationExpired", "name", "data", "recurringReservationId", "recurringReservation", "childReservations", "loading", "childReservationsLoading", "error", "errorMessage", "includePastReservations", "cancelDialogVisible", "userEmail", "cancelLoading", "fromChildReservation", "highlightReservationNumber", "isMobile", "window", "innerWidth", "props", "id", "type", "String", "Number", "required", "created", "$route", "params", "console", "log", "query", "fromChild", "reservation_number", "addEventListener", "handleResize", "loadRecurringReservation", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "activated", "checkChildReservationUpdates", "methods", "response", "getRecurringReservation", "success", "user_email", "loadChildReservations", "message", "$t", "detail", "getChildReservations", "reservations", "now", "Date", "filter", "reservation", "endTime", "end_datetime", "status", "sort", "a", "b", "numA", "parseInt", "replace", "numB", "length", "$message", "goBack", "fromAdmin", "reservationCode", "localStorage", "setItem", "$router", "push", "path", "fromRecurring", "setTimeout", "removeItem", "viewChildReservation", "reservationNumber", "reservation_code", "child", "recurringId", "code", "userContact", "from", "startTime", "start_datetime", "warning", "editChildReservation", "edit", "isReservationStarted", "cancelChildReservation", "result", "$confirm", "confirmButtonText", "cancelButtonText", "requestData", "warn", "cancelReservation", "saveChildReservationStatus", "location", "reload", "stateKey", "state", "statusText", "getChildStatusText", "statusType", "getChildStatusType", "db<PERSON><PERSON>us", "<PERSON><PERSON><PERSON>us", "timestamp", "getTime", "permanent", "JSON", "stringify", "recurring<PERSON><PERSON><PERSON><PERSON>", "recurringStateStr", "getItem", "recurringState", "parse", "fiveMinutes", "e", "needRefresh", "i", "savedStateStr", "savedState", "showCancelDialog", "confirmCancel", "cancelRecurringReservation", "formatDate", "dateStr", "date", "getFullYear", "getMonth", "padStart", "getDate", "formatTime", "timeStr", "substring", "formatDateTime", "row", "column", "cellValue", "getHours", "getMinutes", "getSeconds", "formatDaysOfWeek", "days", "dayNames", "map", "day", "join", "formatDaysOfMonth", "getPatternText", "pattern", "patterns", "getStatusType", "getStatusText", "start", "end", "getRowClassName"], "sources": ["src/views/reservation/RecurringReservationDetail.vue"], "sourcesContent": ["<template>\n  <div class=\"recurring-reservation-detail\">\n    <div v-if=\"loading\" class=\"loading-container\">\n      <el-skeleton :rows=\"10\" animated />\n    </div>\n\n    <div v-else-if=\"error\" class=\"error-container\">\n      <el-result\n        icon=\"error\"\n        :title=\"$t('common.error')\"\n        :sub-title=\"errorMessage\"\n      >\n        <template #extra>\n          <el-button type=\"primary\" @click=\"$router.push('/')\">{{ $t('common.backToHome') }}</el-button>\n        </template>\n      </el-result>\n    </div>\n\n    <div v-else class=\"content-container\">\n      <div class=\"header-actions\">\n        <el-button icon=\"el-icon-back\" @click=\"goBack\">{{ $t('common.back') }}</el-button>\n        <el-button type=\"danger\" @click=\"showCancelDialog\" :disabled=\"recurringReservation.status === 'cancelled'\">\n          {{ $t('reservation.cancelRecurringReservation') }}\n        </el-button>\n      </div>\n\n      <!-- 循环预约信息卡片 -->\n      <el-card class=\"reservation-card\" shadow=\"hover\">\n        <div slot=\"header\" class=\"card-header\">\n          <span>{{ $t('reservation.recurringReservationDetails') }}</span>\n          <el-tag :type=\"getStatusType(recurringReservation)\" size=\"medium\">\n            {{ getStatusText(recurringReservation) }}\n          </el-tag>\n        </div>\n\n        <div class=\"reservation-info\">\n          <div class=\"info-section\">\n            <h3>{{ $t('reservation.reservationInfo') }}</h3>\n            <div class=\"info-grid\">\n              <div class=\"info-row\">\n                <span class=\"info-label\">{{ $t('reservation.code') }}:</span>\n                <span class=\"info-value\">{{ recurringReservation.reservation_code }}</span>\n              </div>\n              <div class=\"info-row\">\n                <span class=\"info-label\">{{ $t('equipment.name') }}:</span>\n                <span class=\"info-value\">{{ recurringReservation.equipment_name }}</span>\n              </div>\n              <div class=\"info-row\">\n                <span class=\"info-label\">{{ $t('equipment.location') }}:</span>\n                <span class=\"info-value\">{{ recurringReservation.equipment_location || $t('common.notProvided') }}</span>\n              </div>\n              <div class=\"info-row\">\n                <span class=\"info-label\">{{ $t('reservation.pattern') }}:</span>\n                <span class=\"info-value\">{{ getPatternText(recurringReservation.pattern_type) }}</span>\n              </div>\n              <div class=\"info-row\">\n                <span class=\"info-label\">{{ $t('reservation.dateRange') }}:</span>\n                <span class=\"info-value date-range-highlight\">{{ formatDate(recurringReservation.start_date) }} - {{ formatDate(recurringReservation.end_date) }}</span>\n              </div>\n              <div class=\"info-row\">\n                <span class=\"info-label\">{{ $t('reservation.timeRange') }}:</span>\n                <span class=\"info-value time-range-highlight\">{{ formatTime(recurringReservation.start_time) }} - {{ formatTime(recurringReservation.end_time) }}</span>\n              </div>\n              <div class=\"info-row\" v-if=\"recurringReservation.days_of_week && recurringReservation.days_of_week.length > 0\">\n                <span class=\"info-label\">{{ $t('reservation.daysOfWeek') }}:</span>\n                <span class=\"info-value\">{{ formatDaysOfWeek(recurringReservation.days_of_week) }}</span>\n              </div>\n              <div class=\"info-row\" v-if=\"recurringReservation.days_of_month && recurringReservation.days_of_month.length > 0\">\n                <span class=\"info-label\">{{ $t('reservation.daysOfMonth') }}:</span>\n                <span class=\"info-value\">{{ formatDaysOfMonth(recurringReservation.days_of_month) }}</span>\n              </div>\n              <div class=\"info-row\">\n                <span class=\"info-label\">{{ $t('reservation.userName') }}:</span>\n                <span class=\"info-value\">{{ recurringReservation.user_name }}</span>\n              </div>\n              <div class=\"info-row\">\n                <span class=\"info-label\">{{ $t('reservation.userDepartment') }}:</span>\n                <span class=\"info-value\">{{ recurringReservation.user_department }}</span>\n              </div>\n              <div class=\"info-row\">\n                <span class=\"info-label\">{{ $t('reservation.userContact') }}:</span>\n                <span class=\"info-value\">{{ recurringReservation.user_contact }}</span>\n              </div>\n              <div class=\"info-row\" v-if=\"recurringReservation.user_email\">\n                <span class=\"info-label\">{{ $t('reservation.userEmail') }}:</span>\n                <span class=\"info-value\">{{ recurringReservation.user_email }}</span>\n              </div>\n              <div class=\"info-row\">\n                <span class=\"info-label\">{{ $t('reservation.purpose') }}:</span>\n                <span class=\"info-value\">{{ recurringReservation.purpose || $t('common.notProvided') }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </el-card>\n\n      <!-- 子预约列表 -->\n      <el-card class=\"child-reservations-card\" shadow=\"hover\">\n        <div slot=\"header\" class=\"card-header\">\n          <span>{{ $t('reservation.childReservations') }}</span>\n          <el-switch\n            v-model=\"includePastReservations\"\n            :active-text=\"$t('reservation.includePast')\"\n            @change=\"loadChildReservations\"\n          ></el-switch>\n        </div>\n\n        <div v-if=\"childReservationsLoading\" class=\"loading-container\">\n          <el-skeleton :rows=\"5\" animated />\n        </div>\n\n        <div v-else-if=\"childReservations.length === 0\" class=\"empty-state\">\n          <el-empty :description=\"$t('reservation.noChildReservations')\"></el-empty>\n        </div>\n\n        <div v-else class=\"child-reservations-list\">\n          <!-- 桌面端表格视图 -->\n          <el-table\n            v-if=\"!isMobile\"\n            :data=\"childReservations\"\n            style=\"width: 100%\"\n            border\n            stripe\n            :row-class-name=\"getRowClassName\"\n          >\n            <el-table-column\n              type=\"index\"\n              :label=\"$t('common.id')\"\n              width=\"60\"\n            ></el-table-column>\n            <el-table-column\n              prop=\"reservation_number\"\n              :label=\"$t('reservation.number')\"\n              width=\"160\"\n            ></el-table-column>\n            <el-table-column\n              prop=\"reservation_code\"\n              :label=\"$t('reservation.code')\"\n              width=\"120\"\n            ></el-table-column>\n            <el-table-column\n              prop=\"start_datetime\"\n              :label=\"$t('reservation.startTime')\"\n              width=\"150\"\n              :formatter=\"formatDateTime\"\n            ></el-table-column>\n            <el-table-column\n              prop=\"end_datetime\"\n              :label=\"$t('reservation.endTime')\"\n              width=\"150\"\n              :formatter=\"formatDateTime\"\n            ></el-table-column>\n            <el-table-column\n              prop=\"status\"\n              :label=\"$t('reservation.status')\"\n              width=\"140\"\n            >\n              <template slot-scope=\"scope\">\n                <el-tag :type=\"getChildStatusType(scope.row)\" size=\"medium\">\n                  {{ getChildStatusText(scope.row) }}\n                </el-tag>\n              </template>\n            </el-table-column>\n            <el-table-column\n              :label=\"$t('common.actions')\"\n              width=\"240\"\n            >\n              <template slot-scope=\"scope\">\n                <el-button\n                  size=\"mini\"\n                  type=\"primary\"\n                  @click=\"viewChildReservation(scope.row)\"\n                  icon=\"el-icon-view\"\n                  circle\n                ></el-button>\n                <el-button\n                  size=\"mini\"\n                  type=\"warning\"\n                  @click=\"editChildReservation(scope.row)\"\n                  :disabled=\"scope.row.status === 'cancelled' || scope.row.status === 'expired' || isReservationStarted(scope.row)\"\n                  icon=\"el-icon-edit\"\n                  circle\n                ></el-button>\n                <el-button\n                  size=\"mini\"\n                  type=\"danger\"\n                  @click=\"cancelChildReservation(scope.row)\"\n                  :disabled=\"scope.row.status === 'cancelled' || scope.row.status === 'expired'\"\n                  icon=\"el-icon-delete\"\n                  circle\n                ></el-button>\n              </template>\n            </el-table-column>\n          </el-table>\n\n          <!-- 移动端卡片视图 -->\n          <div v-else class=\"mobile-card-container\">\n            <div\n              v-for=\"(reservation, index) in childReservations\"\n              :key=\"reservation.id\"\n              class=\"child-reservation-mobile-card\"\n              :class=\"{ 'highlighted-card': highlightReservationNumber && reservation.reservation_number === highlightReservationNumber }\"\n            >\n              <div class=\"card-header\">\n                <div class=\"card-title\">\n                  <span class=\"reservation-index\">#{{ index + 1 }}</span>\n                  <el-tag\n                    :type=\"getChildStatusType(reservation)\"\n                    size=\"small\"\n                    class=\"status-tag\"\n                  >\n                    {{ getChildStatusText(reservation) }}\n                  </el-tag>\n                </div>\n                <div class=\"reservation-number\">{{ reservation.reservation_number }}</div>\n              </div>\n\n              <div class=\"card-content\">\n                <div class=\"info-row\">\n                  <span class=\"label\">{{ $t('reservation.code') }}:</span>\n                  <span class=\"value reservation-code-value\">{{ reservation.reservation_code }}</span>\n                </div>\n\n                <div class=\"time-info\">\n                  <div class=\"time-row\">\n                    <i class=\"el-icon-time\"></i>\n                    <span class=\"time-label\">{{ $t('reservation.startTime') }}:</span>\n                    <span class=\"time-value\">{{ formatDateTime(null, null, reservation.start_datetime) }}</span>\n                  </div>\n                  <div class=\"time-row\">\n                    <i class=\"el-icon-time\"></i>\n                    <span class=\"time-label\">{{ $t('reservation.endTime') }}:</span>\n                    <span class=\"time-value\">{{ formatDateTime(null, null, reservation.end_datetime) }}</span>\n                  </div>\n                </div>\n\n                <div class=\"card-actions\">\n                  <el-button\n                    type=\"primary\"\n                    size=\"small\"\n                    @click=\"viewChildReservation(reservation)\"\n                    class=\"action-button\"\n                  >\n                    <i class=\"el-icon-view\"></i>\n                    {{ $t('common.view') }}\n                  </el-button>\n                  <el-button\n                    type=\"warning\"\n                    size=\"small\"\n                    @click=\"editChildReservation(reservation)\"\n                    :disabled=\"reservation.status === 'cancelled' || reservation.status === 'expired' || isReservationStarted(reservation)\"\n                    class=\"action-button\"\n                  >\n                    <i class=\"el-icon-edit\"></i>\n                    {{ $t('common.edit') }}\n                  </el-button>\n                  <el-button\n                    type=\"danger\"\n                    size=\"small\"\n                    @click=\"cancelChildReservation(reservation)\"\n                    :disabled=\"reservation.status === 'cancelled' || reservation.status === 'expired'\"\n                    class=\"action-button\"\n                  >\n                    <i class=\"el-icon-delete\"></i>\n                    {{ $t('common.cancel') }}\n                  </el-button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 取消循环预约对话框 -->\n    <el-dialog\n      :title=\"$t('reservation.cancelRecurringReservation')\"\n      :visible.sync=\"cancelDialogVisible\"\n      width=\"500px\"\n    >\n      <div class=\"cancel-options\">\n        <p>{{ $t('reservation.cancelRecurringReservationConfirm') }}</p>\n        <div class=\"email-input\">\n          <el-form-item :label=\"$t('reservation.userEmail')\" prop=\"userEmail\">\n            <el-input v-model=\"userEmail\" :placeholder=\"$t('reservation.emailForConfirmation')\"></el-input>\n          </el-form-item>\n        </div>\n      </div>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"cancelDialogVisible = false\">{{ $t('common.cancel') }}</el-button>\n        <el-button type=\"danger\" @click=\"confirmCancel\" :loading=\"cancelLoading\">{{ $t('common.confirm') }}</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { recurringReservationApi, reservationApi } from '@/api'\nimport { isReservationExpired } from '@/utils/date'\n\nexport default {\n  name: 'RecurringReservationDetail',\n\n  data() {\n    return {\n      recurringReservationId: null,\n      recurringReservation: null,\n      childReservations: [],\n      loading: true,\n      childReservationsLoading: false,\n      error: false,\n      errorMessage: '',\n      includePastReservations: true, // 默认选中\"包含已过期预约\"\n      cancelDialogVisible: false,\n      userEmail: '',\n      cancelLoading: false,\n      fromChildReservation: false,\n      highlightReservationNumber: null, // 用于高亮显示特定的子预约\n      // 响应式布局相关\n      isMobile: window.innerWidth <= 768\n    }\n  },\n\n  props: {\n    id: {\n      type: [String, Number],\n      required: false\n    }\n  },\n\n  created() {\n    // 优先使用props中的id，如果没有则使用路由参数\n    this.recurringReservationId = this.id || this.$route.params.id\n    console.log('Recurring reservation ID:', this.recurringReservationId)\n\n    // 检查是否是从子预约详情页面返回的\n    this.fromChildReservation = this.$route.query.fromChild === 'true'\n\n    // 检查是否有预约序号参数，用于高亮显示特定的子预约\n    if (this.$route.query.reservation_number) {\n      this.highlightReservationNumber = this.$route.query.reservation_number\n      console.log('高亮显示预约序号:', this.highlightReservationNumber)\n    }\n\n    // 添加窗口大小变化的监听器\n    window.addEventListener('resize', this.handleResize)\n\n    this.loadRecurringReservation()\n  },\n\n  beforeDestroy() {\n    // 移除窗口大小变化的监听器\n    window.removeEventListener('resize', this.handleResize)\n  },\n\n  // 添加activated钩子函数，在组件被激活时调用（如从子预约详情页面返回）\n  activated() {\n    // 检查子预约状态是否发生变化\n    this.checkChildReservationUpdates()\n  },\n\n  methods: {\n    // 处理窗口大小变化\n    handleResize() {\n      this.isMobile = window.innerWidth <= 768\n    },\n\n    // 加载循环预约详情\n    async loadRecurringReservation() {\n      this.loading = true\n      this.error = false\n\n      try {\n        const response = await recurringReservationApi.getRecurringReservation(this.recurringReservationId)\n\n        if (response.data.success) {\n          this.recurringReservation = response.data.data\n          this.userEmail = this.recurringReservation.user_email || ''\n          this.loadChildReservations()\n        } else {\n          this.error = true\n          this.errorMessage = response.data.message || this.$t('reservation.failedToLoadReservation')\n        }\n      } catch (error) {\n        console.error('Failed to load recurring reservation:', error)\n        this.error = true\n        this.errorMessage = error.response?.data?.detail || this.$t('reservation.failedToLoadReservation')\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 加载子预约列表\n    async loadChildReservations() {\n      this.childReservationsLoading = true\n\n      try {\n        // 始终包含已过期的预约，设置include_past=1\n        const response = await recurringReservationApi.getChildReservations(\n          this.recurringReservationId,\n          1  // 始终设置为1，表示包含已过期的预约\n        )\n\n        if (response.data.success) {\n          // 获取所有子预约\n          this.childReservations = response.data.reservations || [];\n\n          // 如果includePastReservations为false，则在前端过滤掉已过期的预约\n          if (!this.includePastReservations) {\n            const now = new Date();\n            this.childReservations = this.childReservations.filter(reservation => {\n              // 保留未过期的预约和已取消的预约\n              const endTime = new Date(reservation.end_datetime);\n              return endTime >= now || reservation.status === 'cancelled';\n            });\n          }\n\n          // 按预约序号排序\n          this.childReservations.sort((a, b) => {\n            // 从预约序号中提取数字部分进行比较\n            const numA = a.reservation_number ? parseInt(a.reservation_number.replace(/\\D/g, '')) : 0;\n            const numB = b.reservation_number ? parseInt(b.reservation_number.replace(/\\D/g, '')) : 0;\n            return numA - numB;\n          });\n\n          console.log('Child reservations loaded:', this.childReservations.length)\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.failedToLoadChildReservations'))\n        }\n      } catch (error) {\n        console.error('Failed to load child reservations:', error)\n        this.$message.error(this.$t('reservation.failedToLoadChildReservations'))\n      } finally {\n        this.childReservationsLoading = false\n      }\n    },\n\n    // 返回上一页\n    goBack() {\n      // 检查是否有来源页面参数\n      const fromAdmin = this.$route.query.fromAdmin === 'true';\n      const reservationCode = this.$route.query.reservationCode;\n\n      // 设置一个标记，表示这个页面是从循环预约详情页面返回的\n      if (fromAdmin) {\n        localStorage.setItem('returning_from_recurring', 'true');\n      }\n\n      if (fromAdmin && reservationCode) {\n        // 如果是从管理员预约详情页面来的，返回到该页面，并传递fromRecurring参数\n        this.$router.push({\n          path: `/admin/reservation/${reservationCode}`,\n          query: {\n            fromRecurring: 'true'\n          }\n        });\n      } else if (fromAdmin) {\n        // 如果只知道是从管理员页面来的，但没有具体预约码，返回到管理员预约列表\n        this.$router.push('/admin/reservation');\n      } else {\n        // 默认返回到个人预约管理页面\n        this.$router.push('/reservation/query');\n      }\n\n      // 设置一个定时器，在一段时间后清除标记\n      setTimeout(() => {\n        localStorage.removeItem('returning_from_recurring');\n      }, 2000);\n    },\n\n    // 查看子预约详情\n    viewChildReservation(reservation) {\n      // 检查是否有预约序号\n      if (reservation.reservation_number) {\n        console.log('通过预约序号查看子预约详情:', reservation.reservation_number)\n\n        // 使用预约序号直接跳转到预约详情页面\n        const reservationNumber = reservation.reservation_number;\n        const reservationCode = reservation.reservation_code;\n\n        console.log('准备跳转到预约详情页面:', {\n          reservationNumber,\n          reservationCode\n        });\n\n        // 构建查询参数\n        const query = {\n          child: 'true',\n          recurringId: this.recurringReservation.id,\n          code: reservationCode  // 添加预约码作为查询参数\n        }\n\n        // 保留用户联系方式参数（如果有）\n        if (this.$route.query.userContact) {\n          console.log('viewChildReservation - 保留用户联系方式参数:', this.$route.query.userContact)\n          query.userContact = this.$route.query.userContact\n        }\n\n        // 保留来源参数（如果有）\n        if (this.$route.query.from) {\n          console.log('viewChildReservation - 保留来源参数:', this.$route.query.from)\n          query.from = this.$route.query.from\n        }\n\n        console.log('viewChildReservation - 跳转到子预约详情页面，参数:', query)\n        this.$router.push({\n          path: `/reservation/number/${reservationNumber}`,\n          query: query\n        })\n\n        // 将预约序号和预约码保存到localStorage，以便在页面刷新后仍然可以使用\n        localStorage.setItem('current_reservation_number', reservation.reservation_number)\n        localStorage.setItem('current_reservation_code', reservation.reservation_code)\n\n        console.log('查看子预约详情:', {\n          id: reservation.id,\n          reservation_code: reservation.reservation_code,\n          reservation_number: reservation.reservation_number,\n          startTime: reservation.start_datetime,\n          endTime: reservation.end_datetime\n        })\n      } else if (reservation.id) {\n        // 如果没有预约序号但有ID，则使用旧方法\n        // 构建查询参数\n        const query = {\n          child: 'true',\n          id: reservation.id,\n          recurringId: this.recurringReservation.id,\n          startTime: reservation.start_datetime,\n          endTime: reservation.end_datetime\n        }\n\n        // 保留用户联系方式参数（如果有）\n        if (this.$route.query.userContact) {\n          console.log('viewChildReservation(旧方法) - 保留用户联系方式参数:', this.$route.query.userContact)\n          query.userContact = this.$route.query.userContact\n        }\n\n        // 保留来源参数（如果有）\n        if (this.$route.query.from) {\n          console.log('viewChildReservation(旧方法) - 保留来源参数:', this.$route.query.from)\n          query.from = this.$route.query.from\n        }\n\n        console.log('viewChildReservation(旧方法) - 跳转到子预约详情页面，参数:', query)\n        this.$router.push({\n          path: `/reservation/${reservation.reservation_code}`,\n          query: query\n        })\n\n        console.log('使用旧方法查看子预约详情:', {\n          id: reservation.id,\n          reservation_code: reservation.reservation_code,\n          startTime: reservation.start_datetime,\n          endTime: reservation.end_datetime\n        })\n      } else {\n        this.$message.warning('无法查看子预约详情，缺少预约序号和ID')\n      }\n    },\n\n    // 修改子预约\n    editChildReservation(reservation) {\n      // 检查是否有预约序号\n      if (reservation.reservation_number) {\n        console.log('修改子预约:', reservation.reservation_number)\n\n        // 构建查询参数，直接跳转到预约详情页面并进入编辑模式\n        const query = {\n          edit: 'true',  // 添加编辑标记，让预约详情页面直接进入编辑模式\n          from: 'recurring'  // 标记来源是循环预约\n        }\n\n        // 保留用户联系方式参数（如果有）\n        if (this.$route.query.userContact) {\n          query.userContact = this.$route.query.userContact\n        }\n\n        console.log('editChildReservation - 跳转到子预约详情页面（编辑模式），参数:', query)\n\n        // 跳转到预约详情页面，使用预约序号\n        this.$router.push({\n          path: `/reservation/detail/${reservation.reservation_number}`,\n          query: query\n        })\n      } else {\n        this.$message.warning('无法修改子预约，缺少预约序号')\n      }\n    },\n\n    // 检查预约是否已开始\n    isReservationStarted(reservation) {\n      if (!reservation || !reservation.start_datetime) return false\n\n      const now = new Date()\n      const startTime = new Date(reservation.start_datetime)\n\n      return now >= startTime\n    },\n\n    // 取消子预约\n    async cancelChildReservation(reservation) {\n      try {\n        console.log('准备取消子预约:', reservation)\n        console.log('子预约序号:', reservation.reservation_number)\n\n        const result = await this.$confirm(\n          this.$t('reservation.cancelConfirmation'),\n          this.$t('common.warning'),\n          {\n            confirmButtonText: this.$t('common.confirm'),\n            cancelButtonText: this.$t('common.cancel'),\n            type: 'warning'\n          }\n        )\n\n        if (result === 'confirm') {\n          // 添加预约序号参数，确保只取消特定的子预约\n          const requestData = {\n            user_email: this.recurringReservation.user_email\n          }\n\n          // 添加预约序号参数，确保只取消特定的子预约\n          if (reservation.reservation_number) {\n            requestData.reservation_number = reservation.reservation_number\n            console.log('预约序号参数存在:', reservation.reservation_number)\n          } else {\n            console.warn('预约序号参数不存在，将取消所有具有相同预约码的预约')\n          }\n\n          console.log('取消子预约请求参数:', requestData)\n          console.log('预约码:', reservation.reservation_code)\n\n          const response = await reservationApi.cancelReservation(reservation.reservation_code, requestData)\n\n          console.log('取消子预约响应:', response.data)\n\n          if (response.data.success) {\n            this.$message.success(this.$t('reservation.cancelSuccess'))\n\n            // 直接更新子预约状态为已取消\n            reservation.status = 'cancelled'\n\n            // 保存状态变更到localStorage\n            this.saveChildReservationStatus(reservation, 'cancelled')\n\n            // 强制刷新整个页面，确保获取最新数据\n            console.log('子预约已取消，即将刷新页面...')\n            setTimeout(() => {\n              window.location.reload(true)\n            }, 1000)\n          } else {\n            this.$message.error(response.data.message || this.$t('reservation.cancelFailed'))\n          }\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('Failed to cancel reservation:', error)\n          this.$message.error(this.$t('reservation.cancelFailed'))\n        }\n      }\n    },\n\n    // 保存子预约状态到localStorage\n    saveChildReservationStatus(reservation, status) {\n      if (!reservation) return\n\n      // 使用预约码作为键\n      const stateKey = `reservation_status_${reservation.reservation_code}`\n\n      // 保存状态信息\n      const state = {\n        statusText: this.getChildStatusText({ ...reservation, status: status }),\n        statusType: this.getChildStatusType({ ...reservation, status: status }),\n        dbStatus: status,\n        forcedStatus: status,\n        timestamp: new Date().getTime(),\n        permanent: true,\n        reservationCode: reservation.reservation_code\n      }\n\n      console.log('保存子预约状态到localStorage:', state)\n      localStorage.setItem(stateKey, JSON.stringify(state))\n    },\n\n    // 检查子预约状态是否发生变化\n    checkChildReservationUpdates() {\n      console.log('检查子预约状态是否发生变化')\n\n      // 检查是否有子预约状态变更标记\n      const recurringStateKey = `recurring_reservation_${this.recurringReservationId}_child_status_changed`\n      const recurringStateStr = localStorage.getItem(recurringStateKey)\n\n      if (recurringStateStr) {\n        try {\n          const recurringState = JSON.parse(recurringStateStr)\n\n          // 检查状态变更是否还是新鲜的（5分钟内）\n          const now = new Date().getTime()\n          const fiveMinutes = 5 * 60 * 1000\n\n          if (now - recurringState.timestamp <= fiveMinutes) {\n            console.log('检测到子预约状态变更，刷新列表:', recurringState)\n\n            // 移除状态变更标记\n            localStorage.removeItem(recurringStateKey)\n\n            // 重新加载子预约列表\n            this.loadChildReservations()\n            return\n          } else {\n            // 如果状态变更过期，移除它\n            localStorage.removeItem(recurringStateKey)\n          }\n        } catch (e) {\n          console.error('解析子预约状态变更标记时出错:', e)\n        }\n      }\n\n      // 如果没有子预约状态变更标记，检查每个子预约的状态\n      if (this.childReservations.length > 0) {\n        let needRefresh = false\n\n        // 检查每个子预约的状态\n        for (let i = 0; i < this.childReservations.length; i++) {\n          const reservation = this.childReservations[i]\n          const stateKey = `reservation_status_${reservation.reservation_code}`\n          const savedStateStr = localStorage.getItem(stateKey)\n\n          if (savedStateStr) {\n            try {\n              const savedState = JSON.parse(savedStateStr)\n\n              // 检查保存的状态是否还是新鲜的（5分钟内）\n              const now = new Date().getTime()\n              const fiveMinutes = 5 * 60 * 1000\n\n              if (now - savedState.timestamp <= fiveMinutes) {\n                console.log(`检测到子预约 ${reservation.reservation_code} 的状态可能已更改，保存的状态:`, savedState)\n\n                // 如果状态已变更为已取消，需要刷新子预约列表\n                if (savedState.forcedStatus === 'cancelled' ||\n                    (savedState.statusText === this.$t('reservation.cancelled') &&\n                     savedState.statusType === 'danger')) {\n                  console.log(`子预约 ${reservation.reservation_code} 已被标记为已取消，需要刷新列表`)\n                  needRefresh = true\n                  break\n                }\n              } else {\n                // 如果状态过期，则移除它\n                localStorage.removeItem(stateKey)\n              }\n            } catch (e) {\n              console.error(`解析子预约 ${reservation.reservation_code} 的保存状态时出错:`, e)\n            }\n          }\n        }\n\n        // 如果需要刷新，重新加载子预约列表\n        if (needRefresh) {\n          console.log('检测到子预约状态变更，刷新列表')\n          this.loadChildReservations()\n        }\n      }\n    },\n\n    // 显示取消循环预约对话框\n    showCancelDialog() {\n      this.cancelDialogVisible = true\n    },\n\n    // 确认取消循环预约\n    async confirmCancel() {\n      this.cancelLoading = true\n\n      try {\n        const response = await recurringReservationApi.cancelRecurringReservation(\n          this.recurringReservationId,\n          this.userEmail || null\n        )\n\n        if (response.data.success) {\n          this.$message.success(this.$t('reservation.cancelSuccess'))\n          this.cancelDialogVisible = false\n          this.loadRecurringReservation()\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.cancelFailed'))\n        }\n      } catch (error) {\n        console.error('Failed to cancel recurring reservation:', error)\n        this.$message.error(this.$t('reservation.cancelFailed'))\n      } finally {\n        this.cancelLoading = false\n      }\n    },\n\n    // 格式化日期\n    formatDate(dateStr) {\n      if (!dateStr) return ''\n      const date = new Date(dateStr)\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`\n    },\n\n    // 格式化时间\n    formatTime(timeStr) {\n      if (!timeStr) return ''\n      return timeStr.substring(0, 5)\n    },\n\n    // 格式化日期时间\n    formatDateTime(row, column, cellValue) {\n      if (!cellValue) return ''\n      const date = new Date(cellValue)\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`\n    },\n\n    // 格式化星期几\n    formatDaysOfWeek(days) {\n      if (!days || days.length === 0) return ''\n\n      const dayNames = [\n        this.$t('reservation.sunday'),\n        this.$t('reservation.monday'),\n        this.$t('reservation.tuesday'),\n        this.$t('reservation.wednesday'),\n        this.$t('reservation.thursday'),\n        this.$t('reservation.friday'),\n        this.$t('reservation.saturday')\n      ]\n\n      return days.map(day => dayNames[day]).join(', ')\n    },\n\n    // 格式化每月几号\n    formatDaysOfMonth(days) {\n      if (!days || days.length === 0) return ''\n      return days.join(', ')\n    },\n\n    // 获取循环模式文本\n    getPatternText(pattern) {\n      const patterns = {\n        'daily': this.$t('reservation.daily'),\n        'weekly': this.$t('reservation.weekly'),\n        'monthly': this.$t('reservation.monthly'),\n        'custom': this.$t('reservation.custom')\n      }\n      return patterns[pattern] || pattern\n    },\n\n    // 获取循环预约状态类型\n    getStatusType(reservation) {\n      if (reservation.status === 'cancelled') {\n        return 'danger'\n      }\n      return 'success'\n    },\n\n    // 获取循环预约状态文本\n    getStatusText(reservation) {\n      if (reservation.status === 'cancelled') {\n        return this.$t('reservation.cancelled')\n      }\n      return this.$t('reservation.active')\n    },\n\n    // 获取子预约状态类型\n    getChildStatusType(reservation) {\n      // 如果预约已取消，返回红色\n      if (reservation.status === 'cancelled') {\n        return 'danger'\n      }\n\n      // 如果预约已过期，返回橙色\n      if (isReservationExpired(reservation.end_datetime)) {\n        return 'warning'\n      }\n\n      // 如果预约正在进行中，返回蓝色\n      const now = new Date()\n      const start = new Date(reservation.start_datetime)\n      const end = new Date(reservation.end_datetime)\n      if (now >= start && now <= end) {\n        return 'primary'\n      }\n\n      // 如果预约已确认且未开始，返回绿色\n      return 'success'\n    },\n\n    // 获取子预约状态文本\n    getChildStatusText(reservation) {\n      // 如果预约已取消，显示\"已取消\"\n      if (reservation.status === 'cancelled') {\n        return this.$t('reservation.cancelled')\n      }\n\n      // 如果预约已过期，显示\"已过期\"\n      if (isReservationExpired(reservation.end_datetime)) {\n        return this.$t('reservation.expired')\n      }\n\n      // 如果预约正在进行中，显示\"进行中\"\n      const now = new Date()\n      const start = new Date(reservation.start_datetime)\n      const end = new Date(reservation.end_datetime)\n      if (now >= start && now <= end) {\n        return this.$t('reservation.ongoing')\n      }\n\n      // 如果预约已确认且未开始，显示\"已确认\"\n      return this.$t('reservation.confirmed')\n    },\n\n    // 获取行的类名，用于高亮显示特定的子预约\n    getRowClassName({ row }) {\n      if (this.highlightReservationNumber && row.reservation_number === this.highlightReservationNumber) {\n        return 'highlighted-row'\n      }\n      return ''\n    }\n  }\n}\n</script>\n\n<style scoped>\n.recurring-reservation-detail {\n  max-width: 1000px;\n  margin: 0 auto;\n  padding: 20px;\n  padding-bottom: 20px; /* 恢复正常的底部边距 */\n}\n\n.loading-container,\n.error-container {\n  margin: 40px 0;\n}\n\n.header-actions {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20px;\n}\n\n.reservation-card,\n.child-reservations-card {\n  margin-bottom: 30px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.reservation-info {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 20px;\n}\n\n.info-section {\n  margin-bottom: 20px;\n}\n\n.info-section h3 {\n  margin-top: 0;\n  margin-bottom: 15px;\n  font-size: 16px;\n  color: #303133;\n  border-bottom: 1px solid #EBEEF5;\n  padding-bottom: 10px;\n}\n\n.info-row {\n  margin-bottom: 10px;\n  line-height: 1.5;\n}\n\n.info-label {\n  font-weight: bold;\n  color: #606266;\n  margin-right: 5px;\n}\n\n.info-value {\n  color: #303133;\n}\n\n/* 关键信息高亮样式 */\n.reservation-code-highlight {\n  color: #E6A23C !important;\n  font-weight: bold;\n  font-size: 16px;\n  background-color: #FDF6EC;\n  padding: 2px 8px;\n  border-radius: 4px;\n  border: 1px solid #F5DAB1;\n}\n\n.equipment-name-highlight {\n  color: #409EFF !important;\n  font-weight: bold;\n  font-size: 15px;\n}\n\n.location-highlight {\n  color: #67C23A !important;\n  font-weight: 600;\n}\n\n.pattern-highlight {\n  color: #909399 !important;\n  font-weight: 600;\n  background-color: #F4F4F5;\n  padding: 2px 6px;\n  border-radius: 3px;\n}\n\n.date-range-highlight {\n  color: #F56C6C !important;\n  font-weight: bold;\n}\n\n.time-range-highlight {\n  color: #F56C6C !important;\n  font-weight: bold;\n  font-size: 15px;\n}\n\n.days-highlight {\n  color: #E6A23C !important;\n  font-weight: 600;\n}\n\n.user-name-highlight {\n  color: #606266 !important;\n  font-weight: bold;\n}\n\n.contact-highlight {\n  color: #409EFF !important;\n  font-weight: 600;\n}\n\n.email-highlight {\n  color: #409EFF !important;\n  font-weight: 600;\n}\n\n.purpose-highlight {\n  color: #67C23A !important;\n  font-weight: 600;\n  font-style: italic;\n}\n\n.empty-state {\n  padding: 30px 0;\n}\n\n.cancel-options {\n  padding: 0 20px;\n}\n\n.cancel-options p {\n  margin-bottom: 20px;\n  color: #606266;\n}\n\n.email-input {\n  margin-top: 20px;\n}\n\n.child-reservations-list {\n  margin-top: 20px;\n}\n\n.highlighted-row {\n  background-color: #fdf5e6 !important; /* 浅橙色背景 */\n}\n\n/* 移动端卡片样式 */\n.mobile-card-container {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.child-reservation-mobile-card {\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e4e7ed;\n  overflow: hidden;\n  transition: box-shadow 0.3s ease;\n}\n\n.child-reservation-mobile-card:hover {\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.child-reservation-mobile-card.highlighted-card {\n  background-color: #fdf5e6;\n  border: 2px solid #e6a23c;\n  box-shadow: 0 4px 16px rgba(230, 162, 60, 0.2);\n}\n\n.card-header {\n  padding: 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n}\n\n.card-title {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  flex: 1;\n}\n\n.reservation-index {\n  font-size: 16px;\n  font-weight: 600;\n  color: #303133;\n  line-height: 1.4;\n}\n\n.status-tag {\n  align-self: flex-start;\n  font-weight: 500;\n}\n\n.reservation-number {\n  font-size: 12px;\n  color: #409eff;\n  font-weight: 600;\n  margin-left: 12px;\n  background: #ecf5ff;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-family: monospace;\n  border: 1px solid #b3d8ff;\n}\n\n.card-content {\n  padding: 16px;\n}\n\n.info-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 8px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.info-row:last-of-type {\n  border-bottom: none;\n  margin-bottom: 16px;\n}\n\n.info-row .label {\n  font-size: 14px;\n  color: #606266;\n  font-weight: 500;\n  flex-shrink: 0;\n  margin-right: 12px;\n}\n\n.info-row .value {\n  font-size: 14px;\n  color: #303133;\n  text-align: right;\n  word-break: break-word;\n}\n\n.reservation-code-value {\n  color: #E6A23C !important;\n  font-weight: bold !important;\n  background: #FDF6EC;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-family: monospace;\n  border: 1px solid #F5DAB1;\n  font-size: 14px;\n}\n\n.time-info {\n  background: #f8f9fa;\n  border-radius: 6px;\n  padding: 12px;\n  margin-bottom: 16px;\n}\n\n.time-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8px;\n  font-size: 13px;\n}\n\n.time-row:last-child {\n  margin-bottom: 0;\n}\n\n.time-row i {\n  color: #409eff;\n  margin-right: 8px;\n  font-size: 14px;\n}\n\n.time-label {\n  color: #606266;\n  margin-right: 8px;\n  font-weight: 500;\n  min-width: 60px;\n}\n\n.time-value {\n  color: #303133;\n  font-weight: 500;\n}\n\n.card-actions {\n  display: flex;\n  justify-content: space-between;\n  gap: 12px;\n  padding-top: 8px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.action-button {\n  flex: 1;\n  font-weight: 500;\n}\n\n/* 移动端响应式样式 */\n@media (max-width: 768px) {\n  .recurring-reservation-detail {\n    padding: 10px 4px 20px 4px !important; /* 减少左右边距，正常的底部边距 */\n    max-width: 100%;\n    overflow-x: hidden; /* 防止水平滚动 */\n  }\n\n  /* 头部操作按钮在移动端的优化 */\n  .header-actions {\n    flex-direction: column;\n    gap: 10px;\n    margin-bottom: 15px;\n  }\n\n  .header-actions .el-button {\n    width: 100%;\n    margin: 0;\n  }\n\n  /* 预约信息网格布局在移动端改为单列 */\n  .reservation-info {\n    display: block !important; /* 覆盖网格布局 */\n    gap: 0;\n  }\n\n  .info-section {\n    margin-bottom: 20px;\n    width: 100%;\n  }\n\n  /* 卡片在移动端的优化 */\n  .reservation-card,\n  .child-reservations-card {\n    margin: 0 -2px 20px -2px; /* 让卡片更宽，与其他页面保持一致 */\n    border-radius: 8px;\n  }\n\n  /* 卡片头部在移动端的优化 */\n  .card-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 10px;\n  }\n\n  .card-header span {\n    font-size: 16px;\n    font-weight: 600;\n  }\n\n  /* 信息行在移动端的优化 */\n  .info-row {\n    flex-direction: column;\n    align-items: flex-start;\n    margin-bottom: 12px;\n    padding: 8px 0;\n  }\n\n  .info-label {\n    margin-bottom: 4px;\n    font-size: 14px;\n  }\n\n  .info-value {\n    font-size: 14px;\n    word-break: break-word;\n    width: 100%;\n  }\n\n  /* 移动端卡片容器优化 */\n  .mobile-card-container {\n    margin: 0 -2px; /* 与其他页面保持一致的负边距 */\n    gap: 16px;\n  }\n\n  .child-reservation-mobile-card {\n    margin: 0 2px; /* 给卡片适当的边距 */\n    border-radius: 8px;\n  }\n\n  /* 确保页面内容不会超出屏幕 */\n  .content-container {\n    width: 100%;\n    max-width: 100%;\n    overflow-x: hidden;\n  }\n\n  /* 空状态在移动端的优化 */\n  .empty-state {\n    padding: 20px 0;\n  }\n\n  /* 取消对话框在移动端的优化 */\n  .cancel-options {\n    padding: 0 10px;\n  }\n\n  /* 确保所有文本内容不会超出容器 */\n  * {\n    word-wrap: break-word;\n    overflow-wrap: break-word;\n  }\n\n  /* 对话框在移动端的优化 */\n  .el-dialog {\n    width: 95% !important;\n    margin: 0 auto !important;\n  }\n\n  .el-dialog__body {\n    padding: 15px !important;\n  }\n\n  /* 表单项在移动端的优化 */\n  .el-form-item__label {\n    font-size: 14px !important;\n    margin-bottom: 5px !important;\n  }\n\n  .el-input__inner {\n    font-size: 14px !important;\n  }\n\n  /* 按钮在移动端的优化 */\n  .dialog-footer .el-button {\n    width: 48% !important;\n    margin: 0 1% !important;\n  }\n}\n</style>\n"], "mappings": "AAySA,SAAAA,uBAAA,EAAAC,cAAA;AACA,SAAAC,oBAAA;AAEA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,sBAAA;MACAC,oBAAA;MACAC,iBAAA;MACAC,OAAA;MACAC,wBAAA;MACAC,KAAA;MACAC,YAAA;MACAC,uBAAA;MAAA;MACAC,mBAAA;MACAC,SAAA;MACAC,aAAA;MACAC,oBAAA;MACAC,0BAAA;MAAA;MACA;MACAC,QAAA,EAAAC,MAAA,CAAAC,UAAA;IACA;EACA;EAEAC,KAAA;IACAC,EAAA;MACAC,IAAA,GAAAC,MAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EAEAC,QAAA;IACA;IACA,KAAAtB,sBAAA,QAAAiB,EAAA,SAAAM,MAAA,CAAAC,MAAA,CAAAP,EAAA;IACAQ,OAAA,CAAAC,GAAA,mCAAA1B,sBAAA;;IAEA;IACA,KAAAW,oBAAA,QAAAY,MAAA,CAAAI,KAAA,CAAAC,SAAA;;IAEA;IACA,SAAAL,MAAA,CAAAI,KAAA,CAAAE,kBAAA;MACA,KAAAjB,0BAAA,QAAAW,MAAA,CAAAI,KAAA,CAAAE,kBAAA;MACAJ,OAAA,CAAAC,GAAA,mBAAAd,0BAAA;IACA;;IAEA;IACAE,MAAA,CAAAgB,gBAAA,gBAAAC,YAAA;IAEA,KAAAC,wBAAA;EACA;EAEAC,cAAA;IACA;IACAnB,MAAA,CAAAoB,mBAAA,gBAAAH,YAAA;EACA;EAEA;EACAI,UAAA;IACA;IACA,KAAAC,4BAAA;EACA;EAEAC,OAAA;IACA;IACAN,aAAA;MACA,KAAAlB,QAAA,GAAAC,MAAA,CAAAC,UAAA;IACA;IAEA;IACA,MAAAiB,yBAAA;MACA,KAAA7B,OAAA;MACA,KAAAE,KAAA;MAEA;QACA,MAAAiC,QAAA,SAAA3C,uBAAA,CAAA4C,uBAAA,MAAAvC,sBAAA;QAEA,IAAAsC,QAAA,CAAAvC,IAAA,CAAAyC,OAAA;UACA,KAAAvC,oBAAA,GAAAqC,QAAA,CAAAvC,IAAA,CAAAA,IAAA;UACA,KAAAU,SAAA,QAAAR,oBAAA,CAAAwC,UAAA;UACA,KAAAC,qBAAA;QACA;UACA,KAAArC,KAAA;UACA,KAAAC,YAAA,GAAAgC,QAAA,CAAAvC,IAAA,CAAA4C,OAAA,SAAAC,EAAA;QACA;MACA,SAAAvC,KAAA;QACAoB,OAAA,CAAApB,KAAA,0CAAAA,KAAA;QACA,KAAAA,KAAA;QACA,KAAAC,YAAA,GAAAD,KAAA,CAAAiC,QAAA,EAAAvC,IAAA,EAAA8C,MAAA,SAAAD,EAAA;MACA;QACA,KAAAzC,OAAA;MACA;IACA;IAEA;IACA,MAAAuC,sBAAA;MACA,KAAAtC,wBAAA;MAEA;QACA;QACA,MAAAkC,QAAA,SAAA3C,uBAAA,CAAAmD,oBAAA,CACA,KAAA9C,sBAAA,EACA;QACA;QAEA,IAAAsC,QAAA,CAAAvC,IAAA,CAAAyC,OAAA;UACA;UACA,KAAAtC,iBAAA,GAAAoC,QAAA,CAAAvC,IAAA,CAAAgD,YAAA;;UAEA;UACA,UAAAxC,uBAAA;YACA,MAAAyC,GAAA,OAAAC,IAAA;YACA,KAAA/C,iBAAA,QAAAA,iBAAA,CAAAgD,MAAA,CAAAC,WAAA;cACA;cACA,MAAAC,OAAA,OAAAH,IAAA,CAAAE,WAAA,CAAAE,YAAA;cACA,OAAAD,OAAA,IAAAJ,GAAA,IAAAG,WAAA,CAAAG,MAAA;YACA;UACA;;UAEA;UACA,KAAApD,iBAAA,CAAAqD,IAAA,EAAAC,CAAA,EAAAC,CAAA;YACA;YACA,MAAAC,IAAA,GAAAF,CAAA,CAAA3B,kBAAA,GAAA8B,QAAA,CAAAH,CAAA,CAAA3B,kBAAA,CAAA+B,OAAA;YACA,MAAAC,IAAA,GAAAJ,CAAA,CAAA5B,kBAAA,GAAA8B,QAAA,CAAAF,CAAA,CAAA5B,kBAAA,CAAA+B,OAAA;YACA,OAAAF,IAAA,GAAAG,IAAA;UACA;UAEApC,OAAA,CAAAC,GAAA,oCAAAxB,iBAAA,CAAA4D,MAAA;QACA;UACA,KAAAC,QAAA,CAAA1D,KAAA,CAAAiC,QAAA,CAAAvC,IAAA,CAAA4C,OAAA,SAAAC,EAAA;QACA;MACA,SAAAvC,KAAA;QACAoB,OAAA,CAAApB,KAAA,uCAAAA,KAAA;QACA,KAAA0D,QAAA,CAAA1D,KAAA,MAAAuC,EAAA;MACA;QACA,KAAAxC,wBAAA;MACA;IACA;IAEA;IACA4D,OAAA;MACA;MACA,MAAAC,SAAA,QAAA1C,MAAA,CAAAI,KAAA,CAAAsC,SAAA;MACA,MAAAC,eAAA,QAAA3C,MAAA,CAAAI,KAAA,CAAAuC,eAAA;;MAEA;MACA,IAAAD,SAAA;QACAE,YAAA,CAAAC,OAAA;MACA;MAEA,IAAAH,SAAA,IAAAC,eAAA;QACA;QACA,KAAAG,OAAA,CAAAC,IAAA;UACAC,IAAA,wBAAAL,eAAA;UACAvC,KAAA;YACA6C,aAAA;UACA;QACA;MACA,WAAAP,SAAA;QACA;QACA,KAAAI,OAAA,CAAAC,IAAA;MACA;QACA;QACA,KAAAD,OAAA,CAAAC,IAAA;MACA;;MAEA;MACAG,UAAA;QACAN,YAAA,CAAAO,UAAA;MACA;IACA;IAEA;IACAC,qBAAAxB,WAAA;MACA;MACA,IAAAA,WAAA,CAAAtB,kBAAA;QACAJ,OAAA,CAAAC,GAAA,mBAAAyB,WAAA,CAAAtB,kBAAA;;QAEA;QACA,MAAA+C,iBAAA,GAAAzB,WAAA,CAAAtB,kBAAA;QACA,MAAAqC,eAAA,GAAAf,WAAA,CAAA0B,gBAAA;QAEApD,OAAA,CAAAC,GAAA;UACAkD,iBAAA;UACAV;QACA;;QAEA;QACA,MAAAvC,KAAA;UACAmD,KAAA;UACAC,WAAA,OAAA9E,oBAAA,CAAAgB,EAAA;UACA+D,IAAA,EAAAd,eAAA;QACA;;QAEA;QACA,SAAA3C,MAAA,CAAAI,KAAA,CAAAsD,WAAA;UACAxD,OAAA,CAAAC,GAAA,4CAAAH,MAAA,CAAAI,KAAA,CAAAsD,WAAA;UACAtD,KAAA,CAAAsD,WAAA,QAAA1D,MAAA,CAAAI,KAAA,CAAAsD,WAAA;QACA;;QAEA;QACA,SAAA1D,MAAA,CAAAI,KAAA,CAAAuD,IAAA;UACAzD,OAAA,CAAAC,GAAA,wCAAAH,MAAA,CAAAI,KAAA,CAAAuD,IAAA;UACAvD,KAAA,CAAAuD,IAAA,QAAA3D,MAAA,CAAAI,KAAA,CAAAuD,IAAA;QACA;QAEAzD,OAAA,CAAAC,GAAA,0CAAAC,KAAA;QACA,KAAA0C,OAAA,CAAAC,IAAA;UACAC,IAAA,yBAAAK,iBAAA;UACAjD,KAAA,EAAAA;QACA;;QAEA;QACAwC,YAAA,CAAAC,OAAA,+BAAAjB,WAAA,CAAAtB,kBAAA;QACAsC,YAAA,CAAAC,OAAA,6BAAAjB,WAAA,CAAA0B,gBAAA;QAEApD,OAAA,CAAAC,GAAA;UACAT,EAAA,EAAAkC,WAAA,CAAAlC,EAAA;UACA4D,gBAAA,EAAA1B,WAAA,CAAA0B,gBAAA;UACAhD,kBAAA,EAAAsB,WAAA,CAAAtB,kBAAA;UACAsD,SAAA,EAAAhC,WAAA,CAAAiC,cAAA;UACAhC,OAAA,EAAAD,WAAA,CAAAE;QACA;MACA,WAAAF,WAAA,CAAAlC,EAAA;QACA;QACA;QACA,MAAAU,KAAA;UACAmD,KAAA;UACA7D,EAAA,EAAAkC,WAAA,CAAAlC,EAAA;UACA8D,WAAA,OAAA9E,oBAAA,CAAAgB,EAAA;UACAkE,SAAA,EAAAhC,WAAA,CAAAiC,cAAA;UACAhC,OAAA,EAAAD,WAAA,CAAAE;QACA;;QAEA;QACA,SAAA9B,MAAA,CAAAI,KAAA,CAAAsD,WAAA;UACAxD,OAAA,CAAAC,GAAA,iDAAAH,MAAA,CAAAI,KAAA,CAAAsD,WAAA;UACAtD,KAAA,CAAAsD,WAAA,QAAA1D,MAAA,CAAAI,KAAA,CAAAsD,WAAA;QACA;;QAEA;QACA,SAAA1D,MAAA,CAAAI,KAAA,CAAAuD,IAAA;UACAzD,OAAA,CAAAC,GAAA,6CAAAH,MAAA,CAAAI,KAAA,CAAAuD,IAAA;UACAvD,KAAA,CAAAuD,IAAA,QAAA3D,MAAA,CAAAI,KAAA,CAAAuD,IAAA;QACA;QAEAzD,OAAA,CAAAC,GAAA,+CAAAC,KAAA;QACA,KAAA0C,OAAA,CAAAC,IAAA;UACAC,IAAA,kBAAApB,WAAA,CAAA0B,gBAAA;UACAlD,KAAA,EAAAA;QACA;QAEAF,OAAA,CAAAC,GAAA;UACAT,EAAA,EAAAkC,WAAA,CAAAlC,EAAA;UACA4D,gBAAA,EAAA1B,WAAA,CAAA0B,gBAAA;UACAM,SAAA,EAAAhC,WAAA,CAAAiC,cAAA;UACAhC,OAAA,EAAAD,WAAA,CAAAE;QACA;MACA;QACA,KAAAU,QAAA,CAAAsB,OAAA;MACA;IACA;IAEA;IACAC,qBAAAnC,WAAA;MACA;MACA,IAAAA,WAAA,CAAAtB,kBAAA;QACAJ,OAAA,CAAAC,GAAA,WAAAyB,WAAA,CAAAtB,kBAAA;;QAEA;QACA,MAAAF,KAAA;UACA4D,IAAA;UAAA;UACAL,IAAA;QACA;;QAEA;QACA,SAAA3D,MAAA,CAAAI,KAAA,CAAAsD,WAAA;UACAtD,KAAA,CAAAsD,WAAA,QAAA1D,MAAA,CAAAI,KAAA,CAAAsD,WAAA;QACA;QAEAxD,OAAA,CAAAC,GAAA,gDAAAC,KAAA;;QAEA;QACA,KAAA0C,OAAA,CAAAC,IAAA;UACAC,IAAA,yBAAApB,WAAA,CAAAtB,kBAAA;UACAF,KAAA,EAAAA;QACA;MACA;QACA,KAAAoC,QAAA,CAAAsB,OAAA;MACA;IACA;IAEA;IACAG,qBAAArC,WAAA;MACA,KAAAA,WAAA,KAAAA,WAAA,CAAAiC,cAAA;MAEA,MAAApC,GAAA,OAAAC,IAAA;MACA,MAAAkC,SAAA,OAAAlC,IAAA,CAAAE,WAAA,CAAAiC,cAAA;MAEA,OAAApC,GAAA,IAAAmC,SAAA;IACA;IAEA;IACA,MAAAM,uBAAAtC,WAAA;MACA;QACA1B,OAAA,CAAAC,GAAA,aAAAyB,WAAA;QACA1B,OAAA,CAAAC,GAAA,WAAAyB,WAAA,CAAAtB,kBAAA;QAEA,MAAA6D,MAAA,cAAAC,QAAA,CACA,KAAA/C,EAAA,oCACA,KAAAA,EAAA,oBACA;UACAgD,iBAAA,OAAAhD,EAAA;UACAiD,gBAAA,OAAAjD,EAAA;UACA1B,IAAA;QACA,CACA;QAEA,IAAAwE,MAAA;UACA;UACA,MAAAI,WAAA;YACArD,UAAA,OAAAxC,oBAAA,CAAAwC;UACA;;UAEA;UACA,IAAAU,WAAA,CAAAtB,kBAAA;YACAiE,WAAA,CAAAjE,kBAAA,GAAAsB,WAAA,CAAAtB,kBAAA;YACAJ,OAAA,CAAAC,GAAA,cAAAyB,WAAA,CAAAtB,kBAAA;UACA;YACAJ,OAAA,CAAAsE,IAAA;UACA;UAEAtE,OAAA,CAAAC,GAAA,eAAAoE,WAAA;UACArE,OAAA,CAAAC,GAAA,SAAAyB,WAAA,CAAA0B,gBAAA;UAEA,MAAAvC,QAAA,SAAA1C,cAAA,CAAAoG,iBAAA,CAAA7C,WAAA,CAAA0B,gBAAA,EAAAiB,WAAA;UAEArE,OAAA,CAAAC,GAAA,aAAAY,QAAA,CAAAvC,IAAA;UAEA,IAAAuC,QAAA,CAAAvC,IAAA,CAAAyC,OAAA;YACA,KAAAuB,QAAA,CAAAvB,OAAA,MAAAI,EAAA;;YAEA;YACAO,WAAA,CAAAG,MAAA;;YAEA;YACA,KAAA2C,0BAAA,CAAA9C,WAAA;;YAEA;YACA1B,OAAA,CAAAC,GAAA;YACA+C,UAAA;cACA3D,MAAA,CAAAoF,QAAA,CAAAC,MAAA;YACA;UACA;YACA,KAAApC,QAAA,CAAA1D,KAAA,CAAAiC,QAAA,CAAAvC,IAAA,CAAA4C,OAAA,SAAAC,EAAA;UACA;QACA;MACA,SAAAvC,KAAA;QACA,IAAAA,KAAA;UACAoB,OAAA,CAAApB,KAAA,kCAAAA,KAAA;UACA,KAAA0D,QAAA,CAAA1D,KAAA,MAAAuC,EAAA;QACA;MACA;IACA;IAEA;IACAqD,2BAAA9C,WAAA,EAAAG,MAAA;MACA,KAAAH,WAAA;;MAEA;MACA,MAAAiD,QAAA,yBAAAjD,WAAA,CAAA0B,gBAAA;;MAEA;MACA,MAAAwB,KAAA;QACAC,UAAA,OAAAC,kBAAA;UAAA,GAAApD,WAAA;UAAAG,MAAA,EAAAA;QAAA;QACAkD,UAAA,OAAAC,kBAAA;UAAA,GAAAtD,WAAA;UAAAG,MAAA,EAAAA;QAAA;QACAoD,QAAA,EAAApD,MAAA;QACAqD,YAAA,EAAArD,MAAA;QACAsD,SAAA,MAAA3D,IAAA,GAAA4D,OAAA;QACAC,SAAA;QACA5C,eAAA,EAAAf,WAAA,CAAA0B;MACA;MAEApD,OAAA,CAAAC,GAAA,0BAAA2E,KAAA;MACAlC,YAAA,CAAAC,OAAA,CAAAgC,QAAA,EAAAW,IAAA,CAAAC,SAAA,CAAAX,KAAA;IACA;IAEA;IACAjE,6BAAA;MACAX,OAAA,CAAAC,GAAA;;MAEA;MACA,MAAAuF,iBAAA,iCAAAjH,sBAAA;MACA,MAAAkH,iBAAA,GAAA/C,YAAA,CAAAgD,OAAA,CAAAF,iBAAA;MAEA,IAAAC,iBAAA;QACA;UACA,MAAAE,cAAA,GAAAL,IAAA,CAAAM,KAAA,CAAAH,iBAAA;;UAEA;UACA,MAAAlE,GAAA,OAAAC,IAAA,GAAA4D,OAAA;UACA,MAAAS,WAAA;UAEA,IAAAtE,GAAA,GAAAoE,cAAA,CAAAR,SAAA,IAAAU,WAAA;YACA7F,OAAA,CAAAC,GAAA,qBAAA0F,cAAA;;YAEA;YACAjD,YAAA,CAAAO,UAAA,CAAAuC,iBAAA;;YAEA;YACA,KAAAvE,qBAAA;YACA;UACA;YACA;YACAyB,YAAA,CAAAO,UAAA,CAAAuC,iBAAA;UACA;QACA,SAAAM,CAAA;UACA9F,OAAA,CAAApB,KAAA,oBAAAkH,CAAA;QACA;MACA;;MAEA;MACA,SAAArH,iBAAA,CAAA4D,MAAA;QACA,IAAA0D,WAAA;;QAEA;QACA,SAAAC,CAAA,MAAAA,CAAA,QAAAvH,iBAAA,CAAA4D,MAAA,EAAA2D,CAAA;UACA,MAAAtE,WAAA,QAAAjD,iBAAA,CAAAuH,CAAA;UACA,MAAArB,QAAA,yBAAAjD,WAAA,CAAA0B,gBAAA;UACA,MAAA6C,aAAA,GAAAvD,YAAA,CAAAgD,OAAA,CAAAf,QAAA;UAEA,IAAAsB,aAAA;YACA;cACA,MAAAC,UAAA,GAAAZ,IAAA,CAAAM,KAAA,CAAAK,aAAA;;cAEA;cACA,MAAA1E,GAAA,OAAAC,IAAA,GAAA4D,OAAA;cACA,MAAAS,WAAA;cAEA,IAAAtE,GAAA,GAAA2E,UAAA,CAAAf,SAAA,IAAAU,WAAA;gBACA7F,OAAA,CAAAC,GAAA,WAAAyB,WAAA,CAAA0B,gBAAA,oBAAA8C,UAAA;;gBAEA;gBACA,IAAAA,UAAA,CAAAhB,YAAA,oBACAgB,UAAA,CAAArB,UAAA,UAAA1D,EAAA,6BACA+E,UAAA,CAAAnB,UAAA;kBACA/E,OAAA,CAAAC,GAAA,QAAAyB,WAAA,CAAA0B,gBAAA;kBACA2C,WAAA;kBACA;gBACA;cACA;gBACA;gBACArD,YAAA,CAAAO,UAAA,CAAA0B,QAAA;cACA;YACA,SAAAmB,CAAA;cACA9F,OAAA,CAAApB,KAAA,UAAA8C,WAAA,CAAA0B,gBAAA,cAAA0C,CAAA;YACA;UACA;QACA;;QAEA;QACA,IAAAC,WAAA;UACA/F,OAAA,CAAAC,GAAA;UACA,KAAAgB,qBAAA;QACA;MACA;IACA;IAEA;IACAkF,iBAAA;MACA,KAAApH,mBAAA;IACA;IAEA;IACA,MAAAqH,cAAA;MACA,KAAAnH,aAAA;MAEA;QACA,MAAA4B,QAAA,SAAA3C,uBAAA,CAAAmI,0BAAA,CACA,KAAA9H,sBAAA,EACA,KAAAS,SAAA,QACA;QAEA,IAAA6B,QAAA,CAAAvC,IAAA,CAAAyC,OAAA;UACA,KAAAuB,QAAA,CAAAvB,OAAA,MAAAI,EAAA;UACA,KAAApC,mBAAA;UACA,KAAAwB,wBAAA;QACA;UACA,KAAA+B,QAAA,CAAA1D,KAAA,CAAAiC,QAAA,CAAAvC,IAAA,CAAA4C,OAAA,SAAAC,EAAA;QACA;MACA,SAAAvC,KAAA;QACAoB,OAAA,CAAApB,KAAA,4CAAAA,KAAA;QACA,KAAA0D,QAAA,CAAA1D,KAAA,MAAAuC,EAAA;MACA;QACA,KAAAlC,aAAA;MACA;IACA;IAEA;IACAqH,WAAAC,OAAA;MACA,KAAAA,OAAA;MACA,MAAAC,IAAA,OAAAhF,IAAA,CAAA+E,OAAA;MACA,UAAAC,IAAA,CAAAC,WAAA,MAAA/G,MAAA,CAAA8G,IAAA,CAAAE,QAAA,QAAAC,QAAA,YAAAjH,MAAA,CAAA8G,IAAA,CAAAI,OAAA,IAAAD,QAAA;IACA;IAEA;IACAE,WAAAC,OAAA;MACA,KAAAA,OAAA;MACA,OAAAA,OAAA,CAAAC,SAAA;IACA;IAEA;IACAC,eAAAC,GAAA,EAAAC,MAAA,EAAAC,SAAA;MACA,KAAAA,SAAA;MACA,MAAAX,IAAA,OAAAhF,IAAA,CAAA2F,SAAA;MACA,UAAAX,IAAA,CAAAC,WAAA,MAAA/G,MAAA,CAAA8G,IAAA,CAAAE,QAAA,QAAAC,QAAA,YAAAjH,MAAA,CAAA8G,IAAA,CAAAI,OAAA,IAAAD,QAAA,YAAAjH,MAAA,CAAA8G,IAAA,CAAAY,QAAA,IAAAT,QAAA,YAAAjH,MAAA,CAAA8G,IAAA,CAAAa,UAAA,IAAAV,QAAA,YAAAjH,MAAA,CAAA8G,IAAA,CAAAc,UAAA,IAAAX,QAAA;IACA;IAEA;IACAY,iBAAAC,IAAA;MACA,KAAAA,IAAA,IAAAA,IAAA,CAAAnF,MAAA;MAEA,MAAAoF,QAAA,IACA,KAAAtG,EAAA,wBACA,KAAAA,EAAA,wBACA,KAAAA,EAAA,yBACA,KAAAA,EAAA,2BACA,KAAAA,EAAA,0BACA,KAAAA,EAAA,wBACA,KAAAA,EAAA,yBACA;MAEA,OAAAqG,IAAA,CAAAE,GAAA,CAAAC,GAAA,IAAAF,QAAA,CAAAE,GAAA,GAAAC,IAAA;IACA;IAEA;IACAC,kBAAAL,IAAA;MACA,KAAAA,IAAA,IAAAA,IAAA,CAAAnF,MAAA;MACA,OAAAmF,IAAA,CAAAI,IAAA;IACA;IAEA;IACAE,eAAAC,OAAA;MACA,MAAAC,QAAA;QACA,cAAA7G,EAAA;QACA,eAAAA,EAAA;QACA,gBAAAA,EAAA;QACA,eAAAA,EAAA;MACA;MACA,OAAA6G,QAAA,CAAAD,OAAA,KAAAA,OAAA;IACA;IAEA;IACAE,cAAAvG,WAAA;MACA,IAAAA,WAAA,CAAAG,MAAA;QACA;MACA;MACA;IACA;IAEA;IACAqG,cAAAxG,WAAA;MACA,IAAAA,WAAA,CAAAG,MAAA;QACA,YAAAV,EAAA;MACA;MACA,YAAAA,EAAA;IACA;IAEA;IACA6D,mBAAAtD,WAAA;MACA;MACA,IAAAA,WAAA,CAAAG,MAAA;QACA;MACA;;MAEA;MACA,IAAAzD,oBAAA,CAAAsD,WAAA,CAAAE,YAAA;QACA;MACA;;MAEA;MACA,MAAAL,GAAA,OAAAC,IAAA;MACA,MAAA2G,KAAA,OAAA3G,IAAA,CAAAE,WAAA,CAAAiC,cAAA;MACA,MAAAyE,GAAA,OAAA5G,IAAA,CAAAE,WAAA,CAAAE,YAAA;MACA,IAAAL,GAAA,IAAA4G,KAAA,IAAA5G,GAAA,IAAA6G,GAAA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAtD,mBAAApD,WAAA;MACA;MACA,IAAAA,WAAA,CAAAG,MAAA;QACA,YAAAV,EAAA;MACA;;MAEA;MACA,IAAA/C,oBAAA,CAAAsD,WAAA,CAAAE,YAAA;QACA,YAAAT,EAAA;MACA;;MAEA;MACA,MAAAI,GAAA,OAAAC,IAAA;MACA,MAAA2G,KAAA,OAAA3G,IAAA,CAAAE,WAAA,CAAAiC,cAAA;MACA,MAAAyE,GAAA,OAAA5G,IAAA,CAAAE,WAAA,CAAAE,YAAA;MACA,IAAAL,GAAA,IAAA4G,KAAA,IAAA5G,GAAA,IAAA6G,GAAA;QACA,YAAAjH,EAAA;MACA;;MAEA;MACA,YAAAA,EAAA;IACA;IAEA;IACAkH,gBAAA;MAAApB;IAAA;MACA,SAAA9H,0BAAA,IAAA8H,GAAA,CAAA7G,kBAAA,UAAAjB,0BAAA;QACA;MACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}