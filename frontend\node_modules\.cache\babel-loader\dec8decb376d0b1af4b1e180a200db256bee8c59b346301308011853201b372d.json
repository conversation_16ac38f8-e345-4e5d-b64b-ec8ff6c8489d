{"ast": null, "code": "import { reservationApi, equipmentApi } from '@/api';\nimport { formatDate } from '@/utils/date';\nexport default {\n  name: 'ReservationDetail',\n  props: {\n    isAdmin: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      loading: true,\n      reservation: null,\n      cancelDialogVisible: false,\n      cancelling: false,\n      returnDialogVisible: false,\n      returning: false,\n      modifyDialogVisible: false,\n      modifying: false,\n      timeConflict: false,\n      conflictMessage: '',\n      modifyForm: {\n        startDateTime: '',\n        endDateTime: '',\n        purpose: '',\n        userEmail: ''\n      },\n      modifyRules: {\n        startDateTime: [{\n          required: true,\n          message: this.$t('reservation.startTimeRequired'),\n          trigger: 'change'\n        }],\n        endDateTime: [{\n          required: true,\n          message: this.$t('reservation.endTimeRequired'),\n          trigger: 'change'\n        }],\n        userEmail: [{\n          required: true,\n          message: this.$t('reservation.emailRequired'),\n          trigger: 'blur'\n        }, {\n          type: 'email',\n          message: this.$t('reservation.emailFormat'),\n          trigger: 'blur'\n        }]\n      },\n      dateTimePickerOptions: {\n        disabledDate: this.disabledDate\n      },\n      historyDialogVisible: false,\n      loadingHistory: false,\n      historyRecords: [],\n      // 添加循环预定相关属性\n      recurringReservationId: null,\n      isRecurringReservation: false\n    };\n  },\n  computed: {\n    // 是否可以取消预定\n    canCancel() {\n      if (!this.reservation) return false;\n\n      // 只有确认状态的预定可以取消\n      return this.reservation.status === 'confirmed';\n    },\n    // 是否可以提前归还\n    canReturn() {\n      if (!this.reservation) return false;\n\n      // 只有使用中状态的预定可以提前归还\n      return this.reservation.status === 'in_use';\n    },\n    // 是否可以修改预定\n    canModify() {\n      if (!this.reservation) return false;\n\n      // 只有确认状态且未开始的预定可以修改\n      if (this.reservation.status !== 'confirmed') return false;\n\n      // 检查是否已开始\n      const now = new Date();\n      const startTime = new Date(this.reservation.start_datetime);\n      return startTime > now;\n    },\n    // 处理历史记录，按时间分组并过滤掉不需要显示的字段\n    processedHistoryRecords() {\n      if (!this.historyRecords || this.historyRecords.length === 0) {\n        return [];\n      }\n\n      // 过滤掉 lang 字段的修改记录\n      const filteredRecords = this.historyRecords.filter(record => record.field_name !== 'lang');\n\n      // 按照修改时间分组\n      const groupedRecords = {};\n      filteredRecords.forEach(record => {\n        const timestamp = record.created_at;\n        if (!groupedRecords[timestamp]) {\n          groupedRecords[timestamp] = {\n            timestamp: timestamp,\n            user_type: record.user_type,\n            user_id: record.user_id,\n            records: []\n          };\n        }\n        groupedRecords[timestamp].records.push(record);\n      });\n\n      // 转换为数组并按时间倒序排序\n      return Object.values(groupedRecords).sort((a, b) => {\n        return new Date(b.timestamp) - new Date(a.timestamp);\n      });\n    }\n  },\n  created() {\n    this.fetchReservation();\n  },\n  methods: {\n    // 获取预定详情\n    async fetchReservation() {\n      this.loading = true;\n      try {\n        // 检查是否是通过预约序号查看\n        const reservationNumber = this.$route.params.number;\n        const code = this.$route.params.code;\n        let response;\n        if (reservationNumber) {\n          console.log('通过预约序号查看预约详情:', reservationNumber);\n\n          // 从URL中获取预约码（如果有）\n          const codeFromQuery = this.$route.query.code;\n\n          // 如果URL中有预约码，使用预约码和预约序号查询\n          if (codeFromQuery) {\n            console.log('使用预约码和预约序号查询:', codeFromQuery, reservationNumber);\n\n            // 直接使用预约序号作为参数，不要包装在对象中\n            console.log('直接使用预约序号作为参数:', reservationNumber);\n\n            // 使用预约码和预约序号查询\n            response = await reservationApi.getReservationByCode(codeFromQuery, reservationNumber);\n          } else {\n            // 如果URL中没有预约码，直接使用预约序号查询\n            console.log('直接使用预约序号查询:', reservationNumber);\n\n            // 从localStorage中获取预约码（如果有）\n            const savedCode = localStorage.getItem('current_reservation_code');\n            if (savedCode) {\n              console.log('从localStorage中获取到预约码:', savedCode);\n\n              // 直接使用预约序号作为参数，不要包装在对象中\n              console.log('使用保存的预约码和预约序号查询:', savedCode, reservationNumber);\n\n              // 使用预约码和预约序号查询\n              response = await reservationApi.getReservationByCode(savedCode, reservationNumber);\n            } else {\n              // 如果没有预约码，直接使用预约序号查询\n              response = await reservationApi.getReservationByCode(reservationNumber);\n            }\n          }\n        } else if (this.isAdmin) {\n          // 管理员查询\n          response = await reservationApi.getReservation(code);\n        } else {\n          // 用户查询\n          response = await reservationApi.getReservationByCode(code);\n        }\n        if (response.data.success) {\n          this.reservation = response.data.data;\n          console.log('获取到预约详情:', this.reservation);\n\n          // 检查是否是循环预约的子预约\n          if (this.reservation.recurring_reservation_id) {\n            this.isRecurringReservation = true;\n            this.recurringReservationId = this.reservation.recurring_reservation_id;\n            console.log('这是循环预约的子预约，循环预约ID:', this.recurringReservationId);\n          }\n\n          // 检查是否是从循环预约详情页面跳转过来的\n          const isFromRecurring = this.$route.query.child === 'true' && this.$route.query.recurringId;\n          if (isFromRecurring) {\n            this.recurringReservationId = this.$route.query.recurringId;\n            console.log('从循环预约详情页面跳转过来，循环预约ID:', this.recurringReservationId);\n          }\n\n          // 检查是否需要自动进入编辑模式\n          this.$nextTick(() => {\n            if (this.$route.query.edit === 'true' && this.canModify) {\n              console.log('自动进入编辑模式');\n              this.showModifyDialog();\n            }\n          });\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.notFound'));\n          this.reservation = null;\n        }\n      } catch (error) {\n        console.error('Failed to fetch reservation:', error);\n        this.$message.error(this.$t('common.error'));\n        this.reservation = null;\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 返回上一页\n    goBack() {\n      console.log('ReservationDetail.goBack() - 当前路由参数:', this.$route.query);\n\n      // 检查是否是从循环预约详情页面跳转过来的\n      const isFromRecurring = this.$route.query.child === 'true' && this.recurringReservationId;\n      if (isFromRecurring) {\n        // 如果是从循环预约详情页面跳转过来的，返回到循环预约详情页面\n        console.log('ReservationDetail.goBack() - 返回到循环预约详情页面:', this.recurringReservationId);\n\n        // 构建查询参数，保留用户联系方式等信息\n        const query = {\n          fromChild: 'true',\n          reservation_number: this.reservation.reservation_number\n        };\n\n        // 保留用户联系方式参数（如果有）\n        if (this.$route.query.userContact) {\n          query.userContact = this.$route.query.userContact;\n        }\n\n        // 保留来源参数（如果有）\n        if (this.$route.query.from) {\n          query.from = this.$route.query.from;\n        }\n        this.$router.push({\n          path: `/recurring-reservation/${this.recurringReservationId}`,\n          query: query\n        });\n      } else {\n        // 否则返回到个人预约管理页面\n        console.log('ReservationDetail.goBack() - 返回到个人预约管理页面');\n\n        // 检查是否有查询参数，如果有则恢复查询状态\n        if (this.$route.query.from === 'query' && (this.$route.query.userContact || this.$route.query.reservationCode)) {\n          // 构建查询参数来恢复查询状态\n          const queryParams = {};\n          if (this.$route.query.userContact) {\n            queryParams.userContact = this.$route.query.userContact;\n          }\n          if (this.$route.query.reservationCode) {\n            queryParams.reservationCode = this.$route.query.reservationCode;\n          }\n\n          // 添加一个标记表示需要自动执行查询\n          queryParams.autoQuery = 'true';\n          console.log('ReservationDetail.goBack() - 恢复查询状态:', queryParams);\n          this.$router.push({\n            path: '/reservation/query',\n            query: queryParams\n          });\n        } else {\n          // 没有查询参数，直接返回空白查询页面\n          this.$router.push('/reservation/query');\n        }\n      }\n    },\n    // 格式化日期时间\n    formatDateTime(datetime) {\n      return formatDate(datetime, 'YYYY-MM-DD HH:mm:ss', false); // 设置toBeijingTime为false，不进行时区转换\n    },\n    // 获取状态类名\n    getStatusClass(status) {\n      const statusMap = {\n        confirmed: 'status-confirmed',\n        cancelled: 'status-cancelled',\n        completed: 'status-completed',\n        in_use: 'status-in-use',\n        expired: 'status-expired'\n      };\n      return statusMap[status] || 'status-unknown';\n    },\n    // 获取状态图标\n    getStatusIcon(status) {\n      const iconMap = {\n        confirmed: 'el-icon-check',\n        cancelled: 'el-icon-close',\n        completed: 'el-icon-success',\n        in_use: 'el-icon-time',\n        expired: 'el-icon-warning'\n      };\n      return iconMap[status] || 'el-icon-question';\n    },\n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        confirmed: this.$t('reservation.statusConfirmed'),\n        cancelled: this.$t('reservation.statusCancelled'),\n        completed: this.$t('reservation.statusCompleted'),\n        in_use: this.$t('reservation.statusInUse'),\n        expired: this.$t('reservation.statusExpired')\n      };\n      return statusMap[status] || this.$t('reservation.statusUnknown');\n    },\n    // 显示取消对话框\n    showCancelDialog() {\n      this.cancelDialogVisible = true;\n    },\n    // 显示提前归还对话框\n    showReturnDialog() {\n      this.returnDialogVisible = true;\n    },\n    // 取消预定\n    async cancelReservation() {\n      this.cancelling = true;\n      try {\n        const response = await reservationApi.cancelReservation(this.reservation.reservation_code, {\n          reservation_number: this.reservation.reservation_number\n        });\n        if (response.data.success) {\n          this.$message.success(this.$t('reservation.cancelSuccess'));\n          this.cancelDialogVisible = false;\n\n          // 设置一个延迟，让用户看到状态变化后再跳转\n          setTimeout(() => {\n            // 检查是否是从循环预约详情页面跳转过来的\n            const isFromRecurring = this.$route.query.child === 'true' && this.recurringReservationId;\n            if (isFromRecurring) {\n              // 如果是从循环预约详情页面跳转过来的，返回到循环预约详情页面\n              const query = {\n                fromChild: 'true',\n                reservation_number: this.reservation.reservation_number\n              };\n\n              // 保留用户联系方式参数（如果有）\n              if (this.$route.query.userContact) {\n                query.userContact = this.$route.query.userContact;\n              }\n              this.$router.push({\n                path: `/recurring-reservation/${this.recurringReservationId}`,\n                query: query\n              });\n            } else {\n              // 否则返回到预约管理页面\n              this.$router.push('/reservation/query');\n            }\n          }, 1500); // 增加延迟时间，让用户有更多时间看到状态变化\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.cancelFailed'));\n        }\n      } catch (error) {\n        console.error('Failed to cancel reservation:', error);\n        this.$message.error(this.$t('reservation.cancelFailed'));\n      } finally {\n        this.cancelling = false;\n      }\n    },\n    // 提前归还设备\n    async returnEquipment() {\n      this.returning = true;\n      try {\n        // 这里应该调用提前归还API，但目前后端可能没有实现\n        // 暂时使用取消预约API代替\n        const response = await reservationApi.cancelReservation(this.reservation.reservation_code, {\n          reservation_number: this.reservation.reservation_number,\n          is_early_return: true\n        });\n        if (response.data.success) {\n          this.$message.success(this.$t('reservation.returnSuccess'));\n          this.returnDialogVisible = false;\n\n          // 设置一个延迟，让用户看到状态变化后再跳转\n          setTimeout(() => {\n            // 检查是否是从循环预约详情页面跳转过来的\n            const isFromRecurring = this.$route.query.child === 'true' && this.recurringReservationId;\n            if (isFromRecurring) {\n              // 如果是从循环预约详情页面跳转过来的，返回到循环预约详情页面\n              const query = {\n                fromChild: 'true',\n                reservation_number: this.reservation.reservation_number\n              };\n\n              // 保留用户联系方式参数（如果有）\n              if (this.$route.query.userContact) {\n                query.userContact = this.$route.query.userContact;\n              }\n              this.$router.push({\n                path: `/recurring-reservation/${this.recurringReservationId}`,\n                query: query\n              });\n            } else {\n              // 否则返回到预约管理页面\n              this.$router.push('/reservation/query');\n            }\n          }, 1500); // 增加延迟时间，让用户有更多时间看到状态变化\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.returnFailed'));\n        }\n      } catch (error) {\n        console.error('Failed to return equipment:', error);\n        this.$message.error(this.$t('reservation.returnFailed'));\n      } finally {\n        this.returning = false;\n      }\n    },\n    // 禁用日期（今天之前的日期不可选）\n    disabledDate(time) {\n      return time.getTime() < Date.now() - 8.64e7; // 8.64e7是一天的毫秒数\n    },\n    // 显示修改对话框\n    showModifyDialog() {\n      // 初始化表单数据\n      this.modifyForm = {\n        startDateTime: this.reservation.start_datetime,\n        endDateTime: this.reservation.end_datetime,\n        purpose: this.reservation.purpose || '',\n        userEmail: this.reservation.user_email || ''\n      };\n\n      // 显示对话框\n      this.modifyDialogVisible = true;\n    },\n    // 验证时间范围\n    validateTimeRange() {\n      const startTime = new Date(this.modifyForm.startDateTime);\n      const endTime = new Date(this.modifyForm.endDateTime);\n      if (startTime >= endTime) {\n        this.$message.error(this.$t('reservation.invalidTime'));\n        return false;\n      }\n      return true;\n    },\n    // 检查时间可用性\n    async checkTimeAvailability() {\n      if (!this.modifyForm.startDateTime || !this.modifyForm.endDateTime) {\n        return;\n      }\n\n      // 添加更严格的验证\n      if (this.modifyForm.startDateTime >= this.modifyForm.endDateTime) {\n        this.$message.warning(this.$t('reservation.invalidTime'));\n        this.timeConflict = true;\n        return;\n      }\n      try {\n        const equipmentId = this.reservation.equipment_id;\n        const startDate = this.modifyForm.startDateTime;\n        const endDate = this.modifyForm.endDateTime;\n\n        // 调用API检查时间可用性\n        const response = await equipmentApi.getAvailability(equipmentId, startDate, endDate);\n\n        // 检查是否有冲突 - 处理API响应格式\n        if (response.data.specific_time_check) {\n          // 如果是具体时间段检查\n          console.log('具体时间段检查结果:', response.data.available);\n          this.timeConflict = response.data.available.includes(false);\n        } else {\n          // 如果是按日期检查\n          console.log('按日期检查结果:', response.data.available);\n          this.timeConflict = response.data.available.includes(false);\n        }\n        if (this.timeConflict) {\n          console.log('检测到时间冲突:', response.data.available);\n\n          // 检查是否是因为达到最大同时预定数量\n          if (response.data.allow_simultaneous && response.data.max_simultaneous > 1) {\n            this.conflictMessage = `该时间段已达到最大同时预定数量(${response.data.max_simultaneous})`;\n          } else {\n            this.conflictMessage = '';\n          }\n          this.$message.warning(this.$t('reservation.timeConflict'));\n        } else {\n          console.log('时间段可用');\n          this.conflictMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to check availability:', error);\n        this.timeConflict = true;\n        this.$message.error(this.$t('common.error'));\n      }\n    },\n    // 查看修改历史\n    async showHistory() {\n      this.historyDialogVisible = true;\n      this.loadingHistory = true;\n      try {\n        // 传递预约码和预约序号\n        const response = await reservationApi.getReservationHistory(this.reservation.reservation_code, this.reservation.reservation_number);\n        if (response.data.success) {\n          this.historyRecords = response.data.data;\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.historyFetchFailed'));\n          this.historyRecords = [];\n        }\n      } catch (error) {\n        console.error('Failed to fetch history:', error);\n        this.$message.error(this.$t('reservation.historyFetchFailed'));\n        this.historyRecords = [];\n      } finally {\n        this.loadingHistory = false;\n      }\n    },\n    // 获取历史记录项类型\n    getHistoryItemType(action) {\n      const typeMap = {\n        'update': 'primary',\n        'status_change': 'success'\n      };\n      return typeMap[action] || 'info';\n    },\n    // 获取历史记录操作文本\n    getHistoryActionText(action) {\n      const actionMap = {\n        'update': this.$t('reservation.modified'),\n        'create': this.$t('reservation.created'),\n        'delete': this.$t('reservation.deleted'),\n        'status_change': this.$t('reservation.statusChanged')\n      };\n      return actionMap[action] || action;\n    },\n    // 获取字段显示名称\n    getFieldDisplayName(fieldName) {\n      const fieldMap = {\n        'start_datetime': this.$t('reservation.startTime'),\n        'end_datetime': this.$t('reservation.endTime'),\n        'purpose': this.$t('reservation.purpose'),\n        'user_email': this.$t('reservation.userEmail'),\n        'status': this.$t('reservation.status')\n      };\n      return fieldMap[fieldName] || fieldName;\n    },\n    // 格式化历史记录值\n    formatHistoryValue(fieldName, value) {\n      if (!value) return '-';\n      if (fieldName === 'start_datetime' || fieldName === 'end_datetime') {\n        return formatDate(value, 'YYYY-MM-DD HH:mm:ss', false); // 设置toBeijingTime为false，不进行时区转换\n      } else if (fieldName === 'status') {\n        return this.getStatusText(value);\n      }\n      return value;\n    },\n    // 查看循环预约详情\n    viewRecurringReservation() {\n      if (!this.reservation || !this.reservation.recurring_reservation_id) return;\n\n      // 跳转到循环预约详情页面，并传递来源信息和预约码\n      this.$router.push({\n        path: `/recurring-reservation/${this.reservation.recurring_reservation_id}`,\n        query: {\n          fromAdmin: this.isAdmin ? 'true' : 'false',\n          reservationCode: this.reservation.reservation_code\n        }\n      });\n    },\n    // 提交修改表单\n    submitModifyForm() {\n      this.$refs.modifyForm.validate(async valid => {\n        if (!valid) return;\n\n        // 验证时间范围\n        if (!this.validateTimeRange()) return;\n\n        // 检查时间冲突\n        if (this.timeConflict) {\n          this.$message.error(this.$t('reservation.timeConflict'));\n          return;\n        }\n        this.modifying = true;\n        try {\n          // 构建更新数据\n          const updateData = {\n            start_datetime: this.modifyForm.startDateTime,\n            end_datetime: this.modifyForm.endDateTime,\n            purpose: this.modifyForm.purpose || undefined,\n            user_email: this.modifyForm.userEmail || undefined,\n            lang: this.$i18n.locale\n          };\n\n          // 调用更新API - 传递预约序号以确保修改正确的子预约\n          const response = await reservationApi.updateReservation(this.reservation.reservation_code, updateData, this.reservation.reservation_number // 传递预约序号\n          );\n          if (response.data.success) {\n            this.$message.success(this.$t('reservation.updateSuccess'));\n            this.modifyDialogVisible = false;\n            // 重新获取预定信息\n            await this.fetchReservation();\n          } else {\n            this.$message.error(response.data.message || this.$t('reservation.updateFailed'));\n          }\n        } catch (error) {\n          console.error('Failed to update reservation:', error);\n          this.$message.error(this.$t('reservation.updateFailed'));\n        } finally {\n          this.modifying = false;\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["reservationApi", "equipmentApi", "formatDate", "name", "props", "isAdmin", "type", "Boolean", "default", "data", "loading", "reservation", "cancelDialogVisible", "cancelling", "returnDialogVisible", "returning", "modifyDialogVisible", "modifying", "timeConflict", "conflictMessage", "modifyForm", "startDateTime", "endDateTime", "purpose", "userEmail", "modifyRules", "required", "message", "$t", "trigger", "dateTimePickerOptions", "disabledDate", "historyDialogVisible", "loadingHistory", "historyRecords", "recurringReservationId", "isRecurringReservation", "computed", "canCancel", "status", "canReturn", "canModify", "now", "Date", "startTime", "start_datetime", "processedHistoryRecords", "length", "filteredRecords", "filter", "record", "field_name", "groupedRecords", "for<PERSON>ach", "timestamp", "created_at", "user_type", "user_id", "records", "push", "Object", "values", "sort", "a", "b", "created", "fetchReservation", "methods", "reservationNumber", "$route", "params", "number", "code", "response", "console", "log", "codeFromQuery", "query", "getReservationByCode", "savedCode", "localStorage", "getItem", "getReservation", "success", "recurring_reservation_id", "isFromRecurring", "child", "recurringId", "$nextTick", "edit", "showModifyDialog", "$message", "error", "goBack", "fromChild", "reservation_number", "userContact", "from", "$router", "path", "reservationCode", "queryParams", "autoQuery", "formatDateTime", "datetime", "getStatusClass", "statusMap", "confirmed", "cancelled", "completed", "in_use", "expired", "getStatusIcon", "iconMap", "getStatusText", "showCancelDialog", "showReturnDialog", "cancelReservation", "reservation_code", "setTimeout", "returnEquipment", "is_early_return", "time", "getTime", "end_datetime", "user_email", "validateTimeRange", "endTime", "checkTimeAvailability", "warning", "equipmentId", "equipment_id", "startDate", "endDate", "getAvailability", "specific_time_check", "available", "includes", "allow_simultaneous", "max_simultaneous", "showHistory", "getReservationHistory", "getHistoryItemType", "action", "typeMap", "getHistoryActionText", "actionMap", "getFieldDisplayName", "fieldName", "fieldMap", "formatHistoryValue", "value", "viewRecurringReservation", "fromAdmin", "submitModifyForm", "$refs", "validate", "valid", "updateData", "undefined", "lang", "$i18n", "locale", "updateReservation"], "sources": ["src/views/reservation/ReservationDetail.vue"], "sourcesContent": ["<template>\n  <div class=\"reservation-detail\">\n    <div v-if=\"loading\" class=\"loading-container\">\n      <el-skeleton :rows=\"10\" animated />\n    </div>\n\n    <div v-else-if=\"!reservation\" class=\"error-container\">\n      <el-result\n        icon=\"error\"\n        :title=\"$t('error.errorMessage')\"\n        :sub-title=\"$t('reservation.reservationNotFound')\"\n      >\n        <template #extra>\n          <el-button type=\"primary\" @click=\"$router.push('/reservation/query')\">\n            {{ $t('reservation.query') }}\n          </el-button>\n        </template>\n      </el-result>\n    </div>\n\n    <div v-else>\n      <!-- 返回按钮 -->\n      <div class=\"back-link\">\n        <el-button icon=\"el-icon-arrow-left\" @click=\"goBack\">\n          {{ $t('common.back') }}\n        </el-button>\n      </div>\n\n      <h1 class=\"page-title\">{{ $t('reservation.detail') }}</h1>\n\n      <!-- 预定状态 -->\n      <div class=\"reservation-status-card\" :class=\"getStatusClass(reservation.status)\">\n        <div class=\"status-icon\">\n          <i :class=\"getStatusIcon(reservation.status)\"></i>\n        </div>\n        <div class=\"status-text\">\n          <h2>{{ getStatusText(reservation.status) }}</h2>\n          <p>{{ $t('reservation.code') }}: {{ reservation.reservation_code }}</p>\n        </div>\n      </div>\n\n      <!-- 预定详情 -->\n      <el-card shadow=\"never\" class=\"detail-card\">\n        <div slot=\"header\">\n          <span>{{ $t('reservation.detail') }}</span>\n        </div>\n\n        <el-descriptions :column=\"1\" border>\n          <el-descriptions-item :label=\"$t('reservation.number')\">\n            {{ reservation.reservation_number || '-' }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.reservationType')\">\n            <el-tag\n              size=\"medium\"\n              :type=\"reservation.recurring_reservation_id ? 'primary' : 'success'\"\n              effect=\"plain\"\n            >\n              {{ reservation.recurring_reservation_id ? $t('reservation.recurringReservation') : $t('reservation.singleReservation') }}\n            </el-tag>\n            <el-button\n              v-if=\"reservation.recurring_reservation_id\"\n              type=\"primary\"\n              size=\"mini\"\n              style=\"margin-left: 10px;\"\n              @click=\"viewRecurringReservation\"\n            >\n              {{ $t('reservation.viewRecurringReservation') }}\n            </el-button>\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.equipmentName')\">\n            {{ reservation.equipment_name }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('equipment.category')\" v-if=\"reservation.equipment_category\">\n            {{ reservation.equipment_category }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('equipment.location')\" v-if=\"reservation.equipment_location\">\n            {{ reservation.equipment_location }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.startTime')\">\n            {{ formatDateTime(reservation.start_datetime) }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.endTime')\">\n            {{ formatDateTime(reservation.end_datetime) }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.purpose')\" v-if=\"reservation.purpose\">\n            {{ reservation.purpose }}\n          </el-descriptions-item>\n        </el-descriptions>\n      </el-card>\n\n      <!-- 用户信息 -->\n      <el-card shadow=\"never\" class=\"user-card\">\n        <div slot=\"header\">\n          <span>{{ $t('common.userInfo') }}</span>\n        </div>\n\n        <el-descriptions :column=\"1\" border>\n          <el-descriptions-item :label=\"$t('reservation.userName')\">\n            {{ reservation.user_name }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.userDepartment')\">\n            {{ reservation.user_department }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.userContact')\">\n            {{ reservation.user_contact }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.userEmail')\" v-if=\"reservation.user_email\">\n            {{ reservation.user_email }}\n          </el-descriptions-item>\n        </el-descriptions>\n      </el-card>\n\n      <!-- 操作按钮 -->\n      <div class=\"action-buttons\">\n        <!-- 已确认状态的预约显示修改按钮 -->\n        <el-button v-if=\"canModify\" type=\"primary\" @click=\"showModifyDialog\" icon=\"el-icon-edit\">\n          {{ $t('reservation.modifyReservation') }}\n        </el-button>\n\n        <!-- 已确认状态的预约显示取消按钮 -->\n        <el-button v-if=\"canCancel\" type=\"danger\" @click=\"showCancelDialog\" icon=\"el-icon-close\">\n          {{ $t('reservation.cancelReservation') }}\n        </el-button>\n\n        <!-- 使用中状态的预约显示提前归还按钮 -->\n        <el-button v-if=\"canReturn\" type=\"primary\" @click=\"showReturnDialog\" icon=\"el-icon-time\">\n          {{ $t('reservation.earlyReturn') }}\n        </el-button>\n\n        <!-- 查看历史记录按钮 -->\n        <el-button type=\"info\" @click=\"showHistory\" icon=\"el-icon-document\">\n          {{ $t('reservation.viewHistory') }}\n        </el-button>\n      </div>\n\n      <!-- 取消预定对话框 -->\n      <el-dialog\n        :title=\"$t('reservation.cancelConfirmation')\"\n        :visible.sync=\"cancelDialogVisible\"\n        width=\"400px\"\n      >\n        <p>{{ $t('reservation.cancelConfirmationMessage') }}</p>\n        <div slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"cancelDialogVisible = false\">{{ $t('common.cancel') }}</el-button>\n          <el-button type=\"danger\" @click=\"cancelReservation\" :loading=\"cancelling\">\n            {{ $t('common.confirm') }}\n          </el-button>\n        </div>\n      </el-dialog>\n\n      <!-- 提前归还对话框 -->\n      <el-dialog\n        :title=\"$t('reservation.earlyReturn')\"\n        :visible.sync=\"returnDialogVisible\"\n        width=\"400px\"\n      >\n        <p>{{ $t('reservation.confirmEarlyReturn') }}</p>\n        <div slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"returnDialogVisible = false\">{{ $t('common.cancel') }}</el-button>\n          <el-button type=\"primary\" @click=\"returnEquipment\" :loading=\"returning\">\n            {{ $t('common.confirm') }}\n          </el-button>\n        </div>\n      </el-dialog>\n\n      <!-- 修改预定对话框 -->\n      <el-dialog\n        :title=\"$t('reservation.modifyReservation')\"\n        :visible.sync=\"modifyDialogVisible\"\n        width=\"600px\"\n      >\n        <el-form\n          ref=\"modifyForm\"\n          :model=\"modifyForm\"\n          :rules=\"modifyRules\"\n          label-width=\"120px\"\n          v-loading=\"modifying\"\n        >\n          <!-- 开始时间 -->\n          <el-form-item :label=\"$t('reservation.startTime')\" prop=\"startDateTime\">\n            <el-date-picker\n              v-model=\"modifyForm.startDateTime\"\n              type=\"datetime\"\n              :placeholder=\"$t('reservation.selectStartTime')\"\n              style=\"width: 100%\"\n              :picker-options=\"dateTimePickerOptions\"\n              value-format=\"yyyy-MM-ddTHH:mm:ss\"\n              format=\"yyyy-MM-dd HH:mm:ss\"\n              @change=\"checkTimeAvailability\"\n            ></el-date-picker>\n          </el-form-item>\n\n          <!-- 结束时间 -->\n          <el-form-item :label=\"$t('reservation.endTime')\" prop=\"endDateTime\">\n            <el-date-picker\n              v-model=\"modifyForm.endDateTime\"\n              type=\"datetime\"\n              :placeholder=\"$t('reservation.selectEndTime')\"\n              style=\"width: 100%\"\n              :picker-options=\"dateTimePickerOptions\"\n              value-format=\"yyyy-MM-ddTHH:mm:ss\"\n              format=\"yyyy-MM-dd HH:mm:ss\"\n              @change=\"checkTimeAvailability\"\n            ></el-date-picker>\n          </el-form-item>\n\n          <!-- 时间冲突提示 -->\n          <el-alert\n            v-if=\"timeConflict\"\n            title=\"所选时间段已被预定\"\n            type=\"error\"\n            :closable=\"false\"\n            show-icon\n            style=\"margin-bottom: 15px;\"\n          >\n            <template v-if=\"conflictMessage\">\n              {{ conflictMessage }}\n            </template>\n          </el-alert>\n\n          <!-- 使用目的 -->\n          <el-form-item :label=\"$t('reservation.purpose')\" prop=\"purpose\">\n            <el-input\n              v-model=\"modifyForm.purpose\"\n              type=\"textarea\"\n              :rows=\"3\"\n              :placeholder=\"$t('reservation.purposePlaceholder')\"\n            ></el-input>\n          </el-form-item>\n\n          <!-- 用户邮箱 -->\n          <el-form-item :label=\"$t('reservation.userEmail')\" prop=\"userEmail\" required>\n            <el-input\n              v-model=\"modifyForm.userEmail\"\n              :placeholder=\"$t('reservation.emailPlaceholder')\"\n            ></el-input>\n          </el-form-item>\n        </el-form>\n\n        <div slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"modifyDialogVisible = false\">{{ $t('common.cancel') }}</el-button>\n          <el-button type=\"primary\" @click=\"submitModifyForm\" :loading=\"modifying\" :disabled=\"timeConflict\">\n            {{ $t('common.confirm') }}\n          </el-button>\n        </div>\n      </el-dialog>\n\n      <!-- 修改历史记录对话框 -->\n      <el-dialog\n        :title=\"$t('reservation.modificationHistory')\"\n        :visible.sync=\"historyDialogVisible\"\n        width=\"700px\"\n      >\n        <div v-loading=\"loadingHistory\">\n          <el-empty v-if=\"processedHistoryRecords.length === 0\" :description=\"$t('reservation.noHistory')\"></el-empty>\n          <el-timeline v-else>\n            <el-timeline-item\n              v-for=\"(group, index) in processedHistoryRecords\"\n              :key=\"index\"\n              type=\"primary\"\n            >\n              <el-card class=\"history-card\">\n                <!-- 修改时间显示在最上面 -->\n                <div class=\"history-time\">\n                  <i class=\"el-icon-time\"></i> {{ formatDateTime(group.timestamp) }}\n                </div>\n\n                <div class=\"history-user\">\n                  {{ group.user_type === 'admin' ? $t('reservation.admin') : $t('reservation.user') }}\n                  {{ group.user_id ? ': ' + group.user_id : '' }}\n                </div>\n\n                <div v-for=\"(record, recordIndex) in group.records\" :key=\"recordIndex\" class=\"history-item\">\n                  <div class=\"history-action\">\n                    {{ getHistoryActionText(record.action) }}\n                    <span class=\"history-field\">{{ getFieldDisplayName(record.field_name) }}</span>\n                  </div>\n                  <div class=\"history-values\">\n                    <div class=\"history-old-value\">\n                      <span class=\"history-label\">{{ $t('reservation.oldValue') }}:</span>\n                      <span>{{ formatHistoryValue(record.field_name, record.old_value) }}</span>\n                    </div>\n                    <div class=\"history-new-value\">\n                      <span class=\"history-label\">{{ $t('reservation.newValue') }}:</span>\n                      <span>{{ formatHistoryValue(record.field_name, record.new_value) }}</span>\n                    </div>\n                  </div>\n                </div>\n              </el-card>\n            </el-timeline-item>\n          </el-timeline>\n        </div>\n      </el-dialog>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { reservationApi, equipmentApi } from '@/api'\nimport { formatDate } from '@/utils/date'\n\nexport default {\n  name: 'ReservationDetail',\n\n  props: {\n    isAdmin: {\n      type: Boolean,\n      default: false\n    }\n  },\n\n  data() {\n    return {\n      loading: true,\n      reservation: null,\n      cancelDialogVisible: false,\n      cancelling: false,\n      returnDialogVisible: false,\n      returning: false,\n      modifyDialogVisible: false,\n      modifying: false,\n      timeConflict: false,\n      conflictMessage: '',\n      modifyForm: {\n        startDateTime: '',\n        endDateTime: '',\n        purpose: '',\n        userEmail: ''\n      },\n      modifyRules: {\n        startDateTime: [\n          { required: true, message: this.$t('reservation.startTimeRequired'), trigger: 'change' }\n        ],\n        endDateTime: [\n          { required: true, message: this.$t('reservation.endTimeRequired'), trigger: 'change' }\n        ],\n        userEmail: [\n          { required: true, message: this.$t('reservation.emailRequired'), trigger: 'blur' },\n          { type: 'email', message: this.$t('reservation.emailFormat'), trigger: 'blur' }\n        ]\n      },\n      dateTimePickerOptions: {\n        disabledDate: this.disabledDate\n      },\n      historyDialogVisible: false,\n      loadingHistory: false,\n      historyRecords: [],\n      // 添加循环预定相关属性\n      recurringReservationId: null,\n      isRecurringReservation: false\n    }\n  },\n\n  computed: {\n    // 是否可以取消预定\n    canCancel() {\n      if (!this.reservation) return false\n\n      // 只有确认状态的预定可以取消\n      return this.reservation.status === 'confirmed'\n    },\n\n    // 是否可以提前归还\n    canReturn() {\n      if (!this.reservation) return false\n\n      // 只有使用中状态的预定可以提前归还\n      return this.reservation.status === 'in_use'\n    },\n\n    // 是否可以修改预定\n    canModify() {\n      if (!this.reservation) return false\n\n      // 只有确认状态且未开始的预定可以修改\n      if (this.reservation.status !== 'confirmed') return false\n\n      // 检查是否已开始\n      const now = new Date()\n      const startTime = new Date(this.reservation.start_datetime)\n\n      return startTime > now\n    },\n\n    // 处理历史记录，按时间分组并过滤掉不需要显示的字段\n    processedHistoryRecords() {\n      if (!this.historyRecords || this.historyRecords.length === 0) {\n        return []\n      }\n\n      // 过滤掉 lang 字段的修改记录\n      const filteredRecords = this.historyRecords.filter(record =>\n        record.field_name !== 'lang'\n      )\n\n      // 按照修改时间分组\n      const groupedRecords = {}\n      filteredRecords.forEach(record => {\n        const timestamp = record.created_at\n        if (!groupedRecords[timestamp]) {\n          groupedRecords[timestamp] = {\n            timestamp: timestamp,\n            user_type: record.user_type,\n            user_id: record.user_id,\n            records: []\n          }\n        }\n        groupedRecords[timestamp].records.push(record)\n      })\n\n      // 转换为数组并按时间倒序排序\n      return Object.values(groupedRecords).sort((a, b) => {\n        return new Date(b.timestamp) - new Date(a.timestamp)\n      })\n    }\n  },\n\n  created() {\n    this.fetchReservation()\n  },\n\n  methods: {\n    // 获取预定详情\n    async fetchReservation() {\n      this.loading = true\n      try {\n        // 检查是否是通过预约序号查看\n        const reservationNumber = this.$route.params.number\n        const code = this.$route.params.code\n        let response\n\n        if (reservationNumber) {\n          console.log('通过预约序号查看预约详情:', reservationNumber)\n\n          // 从URL中获取预约码（如果有）\n          const codeFromQuery = this.$route.query.code\n\n          // 如果URL中有预约码，使用预约码和预约序号查询\n          if (codeFromQuery) {\n            console.log('使用预约码和预约序号查询:', codeFromQuery, reservationNumber)\n\n            // 直接使用预约序号作为参数，不要包装在对象中\n            console.log('直接使用预约序号作为参数:', reservationNumber);\n\n            // 使用预约码和预约序号查询\n            response = await reservationApi.getReservationByCode(codeFromQuery, reservationNumber)\n          } else {\n            // 如果URL中没有预约码，直接使用预约序号查询\n            console.log('直接使用预约序号查询:', reservationNumber)\n\n            // 从localStorage中获取预约码（如果有）\n            const savedCode = localStorage.getItem('current_reservation_code')\n\n            if (savedCode) {\n              console.log('从localStorage中获取到预约码:', savedCode)\n\n              // 直接使用预约序号作为参数，不要包装在对象中\n              console.log('使用保存的预约码和预约序号查询:', savedCode, reservationNumber);\n\n              // 使用预约码和预约序号查询\n              response = await reservationApi.getReservationByCode(savedCode, reservationNumber)\n            } else {\n              // 如果没有预约码，直接使用预约序号查询\n              response = await reservationApi.getReservationByCode(reservationNumber)\n            }\n          }\n        } else if (this.isAdmin) {\n          // 管理员查询\n          response = await reservationApi.getReservation(code)\n        } else {\n          // 用户查询\n          response = await reservationApi.getReservationByCode(code)\n        }\n\n        if (response.data.success) {\n          this.reservation = response.data.data\n          console.log('获取到预约详情:', this.reservation)\n\n          // 检查是否是循环预约的子预约\n          if (this.reservation.recurring_reservation_id) {\n            this.isRecurringReservation = true\n            this.recurringReservationId = this.reservation.recurring_reservation_id\n            console.log('这是循环预约的子预约，循环预约ID:', this.recurringReservationId)\n          }\n\n          // 检查是否是从循环预约详情页面跳转过来的\n          const isFromRecurring = this.$route.query.child === 'true' && this.$route.query.recurringId\n          if (isFromRecurring) {\n            this.recurringReservationId = this.$route.query.recurringId\n            console.log('从循环预约详情页面跳转过来，循环预约ID:', this.recurringReservationId)\n          }\n\n          // 检查是否需要自动进入编辑模式\n          this.$nextTick(() => {\n            if (this.$route.query.edit === 'true' && this.canModify) {\n              console.log('自动进入编辑模式')\n              this.showModifyDialog()\n            }\n          })\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.notFound'))\n          this.reservation = null\n        }\n      } catch (error) {\n        console.error('Failed to fetch reservation:', error)\n        this.$message.error(this.$t('common.error'))\n        this.reservation = null\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 返回上一页\n    goBack() {\n      console.log('ReservationDetail.goBack() - 当前路由参数:', this.$route.query)\n\n      // 检查是否是从循环预约详情页面跳转过来的\n      const isFromRecurring = this.$route.query.child === 'true' && this.recurringReservationId\n\n      if (isFromRecurring) {\n        // 如果是从循环预约详情页面跳转过来的，返回到循环预约详情页面\n        console.log('ReservationDetail.goBack() - 返回到循环预约详情页面:', this.recurringReservationId)\n\n        // 构建查询参数，保留用户联系方式等信息\n        const query = {\n          fromChild: 'true',\n          reservation_number: this.reservation.reservation_number\n        }\n\n        // 保留用户联系方式参数（如果有）\n        if (this.$route.query.userContact) {\n          query.userContact = this.$route.query.userContact\n        }\n\n        // 保留来源参数（如果有）\n        if (this.$route.query.from) {\n          query.from = this.$route.query.from\n        }\n\n        this.$router.push({\n          path: `/recurring-reservation/${this.recurringReservationId}`,\n          query: query\n        })\n      } else {\n        // 否则返回到个人预约管理页面\n        console.log('ReservationDetail.goBack() - 返回到个人预约管理页面')\n\n        // 检查是否有查询参数，如果有则恢复查询状态\n        if (this.$route.query.from === 'query' && (this.$route.query.userContact || this.$route.query.reservationCode)) {\n          // 构建查询参数来恢复查询状态\n          const queryParams = {}\n\n          if (this.$route.query.userContact) {\n            queryParams.userContact = this.$route.query.userContact\n          }\n\n          if (this.$route.query.reservationCode) {\n            queryParams.reservationCode = this.$route.query.reservationCode\n          }\n\n          // 添加一个标记表示需要自动执行查询\n          queryParams.autoQuery = 'true'\n\n          console.log('ReservationDetail.goBack() - 恢复查询状态:', queryParams)\n\n          this.$router.push({\n            path: '/reservation/query',\n            query: queryParams\n          })\n        } else {\n          // 没有查询参数，直接返回空白查询页面\n          this.$router.push('/reservation/query')\n        }\n      }\n    },\n\n    // 格式化日期时间\n    formatDateTime(datetime) {\n      return formatDate(datetime, 'YYYY-MM-DD HH:mm:ss', false) // 设置toBeijingTime为false，不进行时区转换\n    },\n\n    // 获取状态类名\n    getStatusClass(status) {\n      const statusMap = {\n        confirmed: 'status-confirmed',\n        cancelled: 'status-cancelled',\n        completed: 'status-completed',\n        in_use: 'status-in-use',\n        expired: 'status-expired'\n      }\n      return statusMap[status] || 'status-unknown'\n    },\n\n    // 获取状态图标\n    getStatusIcon(status) {\n      const iconMap = {\n        confirmed: 'el-icon-check',\n        cancelled: 'el-icon-close',\n        completed: 'el-icon-success',\n        in_use: 'el-icon-time',\n        expired: 'el-icon-warning'\n      }\n      return iconMap[status] || 'el-icon-question'\n    },\n\n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        confirmed: this.$t('reservation.statusConfirmed'),\n        cancelled: this.$t('reservation.statusCancelled'),\n        completed: this.$t('reservation.statusCompleted'),\n        in_use: this.$t('reservation.statusInUse'),\n        expired: this.$t('reservation.statusExpired')\n      }\n      return statusMap[status] || this.$t('reservation.statusUnknown')\n    },\n\n\n\n    // 显示取消对话框\n    showCancelDialog() {\n      this.cancelDialogVisible = true\n    },\n\n    // 显示提前归还对话框\n    showReturnDialog() {\n      this.returnDialogVisible = true\n    },\n\n    // 取消预定\n    async cancelReservation() {\n      this.cancelling = true\n      try {\n        const response = await reservationApi.cancelReservation(\n          this.reservation.reservation_code,\n          { reservation_number: this.reservation.reservation_number }\n        )\n\n        if (response.data.success) {\n          this.$message.success(this.$t('reservation.cancelSuccess'))\n          this.cancelDialogVisible = false\n\n          // 设置一个延迟，让用户看到状态变化后再跳转\n          setTimeout(() => {\n            // 检查是否是从循环预约详情页面跳转过来的\n            const isFromRecurring = this.$route.query.child === 'true' && this.recurringReservationId\n\n            if (isFromRecurring) {\n              // 如果是从循环预约详情页面跳转过来的，返回到循环预约详情页面\n              const query = {\n                fromChild: 'true',\n                reservation_number: this.reservation.reservation_number\n              }\n\n              // 保留用户联系方式参数（如果有）\n              if (this.$route.query.userContact) {\n                query.userContact = this.$route.query.userContact\n              }\n\n              this.$router.push({\n                path: `/recurring-reservation/${this.recurringReservationId}`,\n                query: query\n              })\n            } else {\n              // 否则返回到预约管理页面\n              this.$router.push('/reservation/query')\n            }\n          }, 1500) // 增加延迟时间，让用户有更多时间看到状态变化\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.cancelFailed'))\n        }\n      } catch (error) {\n        console.error('Failed to cancel reservation:', error)\n        this.$message.error(this.$t('reservation.cancelFailed'))\n      } finally {\n        this.cancelling = false\n      }\n    },\n\n    // 提前归还设备\n    async returnEquipment() {\n      this.returning = true\n      try {\n        // 这里应该调用提前归还API，但目前后端可能没有实现\n        // 暂时使用取消预约API代替\n        const response = await reservationApi.cancelReservation(\n          this.reservation.reservation_code,\n          {\n            reservation_number: this.reservation.reservation_number,\n            is_early_return: true\n          }\n        )\n\n        if (response.data.success) {\n          this.$message.success(this.$t('reservation.returnSuccess'))\n          this.returnDialogVisible = false\n\n          // 设置一个延迟，让用户看到状态变化后再跳转\n          setTimeout(() => {\n            // 检查是否是从循环预约详情页面跳转过来的\n            const isFromRecurring = this.$route.query.child === 'true' && this.recurringReservationId\n\n            if (isFromRecurring) {\n              // 如果是从循环预约详情页面跳转过来的，返回到循环预约详情页面\n              const query = {\n                fromChild: 'true',\n                reservation_number: this.reservation.reservation_number\n              }\n\n              // 保留用户联系方式参数（如果有）\n              if (this.$route.query.userContact) {\n                query.userContact = this.$route.query.userContact\n              }\n\n              this.$router.push({\n                path: `/recurring-reservation/${this.recurringReservationId}`,\n                query: query\n              })\n            } else {\n              // 否则返回到预约管理页面\n              this.$router.push('/reservation/query')\n            }\n          }, 1500) // 增加延迟时间，让用户有更多时间看到状态变化\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.returnFailed'))\n        }\n      } catch (error) {\n        console.error('Failed to return equipment:', error)\n        this.$message.error(this.$t('reservation.returnFailed'))\n      } finally {\n        this.returning = false\n      }\n    },\n\n    // 禁用日期（今天之前的日期不可选）\n    disabledDate(time) {\n      return time.getTime() < Date.now() - 8.64e7 // 8.64e7是一天的毫秒数\n    },\n\n    // 显示修改对话框\n    showModifyDialog() {\n      // 初始化表单数据\n      this.modifyForm = {\n        startDateTime: this.reservation.start_datetime,\n        endDateTime: this.reservation.end_datetime,\n        purpose: this.reservation.purpose || '',\n        userEmail: this.reservation.user_email || ''\n      }\n\n      // 显示对话框\n      this.modifyDialogVisible = true\n    },\n\n    // 验证时间范围\n    validateTimeRange() {\n      const startTime = new Date(this.modifyForm.startDateTime)\n      const endTime = new Date(this.modifyForm.endDateTime)\n\n      if (startTime >= endTime) {\n        this.$message.error(this.$t('reservation.invalidTime'))\n        return false\n      }\n\n      return true\n    },\n\n    // 检查时间可用性\n    async checkTimeAvailability() {\n      if (!this.modifyForm.startDateTime || !this.modifyForm.endDateTime) {\n        return\n      }\n\n      // 添加更严格的验证\n      if (this.modifyForm.startDateTime >= this.modifyForm.endDateTime) {\n        this.$message.warning(this.$t('reservation.invalidTime'))\n        this.timeConflict = true\n        return\n      }\n\n      try {\n        const equipmentId = this.reservation.equipment_id\n        const startDate = this.modifyForm.startDateTime\n        const endDate = this.modifyForm.endDateTime\n\n        // 调用API检查时间可用性\n        const response = await equipmentApi.getAvailability(equipmentId, startDate, endDate)\n\n        // 检查是否有冲突 - 处理API响应格式\n        if (response.data.specific_time_check) {\n          // 如果是具体时间段检查\n          console.log('具体时间段检查结果:', response.data.available)\n          this.timeConflict = response.data.available.includes(false)\n        } else {\n          // 如果是按日期检查\n          console.log('按日期检查结果:', response.data.available)\n          this.timeConflict = response.data.available.includes(false)\n        }\n\n        if (this.timeConflict) {\n          console.log('检测到时间冲突:', response.data.available)\n\n          // 检查是否是因为达到最大同时预定数量\n          if (response.data.allow_simultaneous && response.data.max_simultaneous > 1) {\n            this.conflictMessage = `该时间段已达到最大同时预定数量(${response.data.max_simultaneous})`;\n          } else {\n            this.conflictMessage = '';\n          }\n\n          this.$message.warning(this.$t('reservation.timeConflict'))\n        } else {\n          console.log('时间段可用')\n          this.conflictMessage = '';\n        }\n      } catch (error) {\n        console.error('Failed to check availability:', error)\n        this.timeConflict = true\n        this.$message.error(this.$t('common.error'))\n      }\n    },\n\n    // 查看修改历史\n    async showHistory() {\n      this.historyDialogVisible = true\n      this.loadingHistory = true\n\n      try {\n        // 传递预约码和预约序号\n        const response = await reservationApi.getReservationHistory(\n          this.reservation.reservation_code,\n          this.reservation.reservation_number\n        )\n\n        if (response.data.success) {\n          this.historyRecords = response.data.data\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.historyFetchFailed'))\n          this.historyRecords = []\n        }\n      } catch (error) {\n        console.error('Failed to fetch history:', error)\n        this.$message.error(this.$t('reservation.historyFetchFailed'))\n        this.historyRecords = []\n      } finally {\n        this.loadingHistory = false\n      }\n    },\n\n    // 获取历史记录项类型\n    getHistoryItemType(action) {\n      const typeMap = {\n        'update': 'primary',\n        'status_change': 'success'\n      }\n      return typeMap[action] || 'info'\n    },\n\n    // 获取历史记录操作文本\n    getHistoryActionText(action) {\n      const actionMap = {\n        'update': this.$t('reservation.modified'),\n        'create': this.$t('reservation.created'),\n        'delete': this.$t('reservation.deleted'),\n        'status_change': this.$t('reservation.statusChanged')\n      }\n      return actionMap[action] || action\n    },\n\n    // 获取字段显示名称\n    getFieldDisplayName(fieldName) {\n      const fieldMap = {\n        'start_datetime': this.$t('reservation.startTime'),\n        'end_datetime': this.$t('reservation.endTime'),\n        'purpose': this.$t('reservation.purpose'),\n        'user_email': this.$t('reservation.userEmail'),\n        'status': this.$t('reservation.status')\n      }\n      return fieldMap[fieldName] || fieldName\n    },\n\n    // 格式化历史记录值\n    formatHistoryValue(fieldName, value) {\n      if (!value) return '-'\n\n      if (fieldName === 'start_datetime' || fieldName === 'end_datetime') {\n        return formatDate(value, 'YYYY-MM-DD HH:mm:ss', false) // 设置toBeijingTime为false，不进行时区转换\n      } else if (fieldName === 'status') {\n        return this.getStatusText(value)\n      }\n\n      return value\n    },\n\n    // 查看循环预约详情\n    viewRecurringReservation() {\n      if (!this.reservation || !this.reservation.recurring_reservation_id) return;\n\n      // 跳转到循环预约详情页面，并传递来源信息和预约码\n      this.$router.push({\n        path: `/recurring-reservation/${this.reservation.recurring_reservation_id}`,\n        query: {\n          fromAdmin: this.isAdmin ? 'true' : 'false',\n          reservationCode: this.reservation.reservation_code\n        }\n      });\n    },\n\n    // 提交修改表单\n    submitModifyForm() {\n      this.$refs.modifyForm.validate(async valid => {\n        if (!valid) return\n\n        // 验证时间范围\n        if (!this.validateTimeRange()) return\n\n        // 检查时间冲突\n        if (this.timeConflict) {\n          this.$message.error(this.$t('reservation.timeConflict'))\n          return\n        }\n\n        this.modifying = true\n        try {\n          // 构建更新数据\n          const updateData = {\n            start_datetime: this.modifyForm.startDateTime,\n            end_datetime: this.modifyForm.endDateTime,\n            purpose: this.modifyForm.purpose || undefined,\n            user_email: this.modifyForm.userEmail || undefined,\n            lang: this.$i18n.locale\n          }\n\n          // 调用更新API - 传递预约序号以确保修改正确的子预约\n          const response = await reservationApi.updateReservation(\n            this.reservation.reservation_code,\n            updateData,\n            this.reservation.reservation_number  // 传递预约序号\n          )\n\n          if (response.data.success) {\n            this.$message.success(this.$t('reservation.updateSuccess'))\n            this.modifyDialogVisible = false\n            // 重新获取预定信息\n            await this.fetchReservation()\n          } else {\n            this.$message.error(response.data.message || this.$t('reservation.updateFailed'))\n          }\n        } catch (error) {\n          console.error('Failed to update reservation:', error)\n          this.$message.error(this.$t('reservation.updateFailed'))\n        } finally {\n          this.modifying = false\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.reservation-detail {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n.page-title {\n  font-size: 24px;\n  margin-bottom: 20px;\n  color: #303133;\n}\n\n.back-link {\n  margin-bottom: 20px;\n}\n\n.loading-container {\n  padding: 40px 0;\n  text-align: center;\n}\n\n.error-container {\n  padding: 40px 0;\n}\n\n.reservation-status-card {\n  display: flex;\n  align-items: center;\n  padding: 20px;\n  border-radius: 4px;\n  margin-bottom: 20px;\n  color: white;\n}\n\n.status-icon {\n  font-size: 40px;\n  margin-right: 20px;\n}\n\n.status-text h2 {\n  margin: 0 0 10px 0;\n  font-size: 20px;\n}\n\n.status-text p {\n  margin: 0;\n  font-size: 16px;\n}\n\n.status-confirmed {\n  background-color: #67c23a;\n}\n\n.status-cancelled {\n  background-color: #f56c6c;\n}\n\n.status-completed {\n  background-color: #909399;\n}\n\n.status-in-use {\n  background-color: #409eff;\n}\n\n.status-expired {\n  background-color: #e6a23c;\n}\n\n.status-unknown {\n  background-color: #909399;\n}\n\n.history-card {\n  margin-bottom: 10px;\n}\n\n.history-time {\n  font-weight: bold;\n  margin-bottom: 10px;\n  color: #ff7c40;\n  font-size: 16px;\n  border-bottom: 1px solid #EBEEF5;\n  padding-bottom: 10px;\n}\n\n.history-user {\n  font-weight: bold;\n  margin-bottom: 10px;\n  color: #606266;\n}\n\n.history-item {\n  margin-bottom: 15px;\n  padding-bottom: 15px;\n  border-bottom: 1px dashed #ebeef5;\n}\n\n.history-item:last-child {\n  margin-bottom: 0;\n  padding-bottom: 0;\n  border-bottom: none;\n}\n\n.history-action {\n  font-weight: bold;\n  margin-bottom: 8px;\n  font-size: 14px;\n}\n\n.history-field {\n  color: #409eff;\n}\n\n.history-values {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  padding-left: 10px;\n  font-size: 13px;\n}\n\n.history-old-value, .history-new-value {\n  display: flex;\n  align-items: flex-start;\n}\n\n.history-old-value {\n  color: #F56C6C;\n}\n\n.history-new-value {\n  color: #67C23A;\n}\n\n.history-label {\n  font-weight: bold;\n  margin-right: 10px;\n  min-width: 80px;\n  color: #606266;\n}\n\n.detail-card, .user-card {\n  margin-bottom: 20px;\n}\n\n.action-buttons {\n  margin-top: 30px;\n  text-align: center;\n}\n</style>\n"], "mappings": "AAoTA,SAAAA,cAAA,EAAAC,YAAA;AACA,SAAAC,UAAA;AAEA;EACAC,IAAA;EAEAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EAEAC,KAAA;IACA;MACAC,OAAA;MACAC,WAAA;MACAC,mBAAA;MACAC,UAAA;MACAC,mBAAA;MACAC,SAAA;MACAC,mBAAA;MACAC,SAAA;MACAC,YAAA;MACAC,eAAA;MACAC,UAAA;QACAC,aAAA;QACAC,WAAA;QACAC,OAAA;QACAC,SAAA;MACA;MACAC,WAAA;QACAJ,aAAA,GACA;UAAAK,QAAA;UAAAC,OAAA,OAAAC,EAAA;UAAAC,OAAA;QAAA,EACA;QACAP,WAAA,GACA;UAAAI,QAAA;UAAAC,OAAA,OAAAC,EAAA;UAAAC,OAAA;QAAA,EACA;QACAL,SAAA,GACA;UAAAE,QAAA;UAAAC,OAAA,OAAAC,EAAA;UAAAC,OAAA;QAAA,GACA;UAAAvB,IAAA;UAAAqB,OAAA,OAAAC,EAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,qBAAA;QACAC,YAAA,OAAAA;MACA;MACAC,oBAAA;MACAC,cAAA;MACAC,cAAA;MACA;MACAC,sBAAA;MACAC,sBAAA;IACA;EACA;EAEAC,QAAA;IACA;IACAC,UAAA;MACA,UAAA3B,WAAA;;MAEA;MACA,YAAAA,WAAA,CAAA4B,MAAA;IACA;IAEA;IACAC,UAAA;MACA,UAAA7B,WAAA;;MAEA;MACA,YAAAA,WAAA,CAAA4B,MAAA;IACA;IAEA;IACAE,UAAA;MACA,UAAA9B,WAAA;;MAEA;MACA,SAAAA,WAAA,CAAA4B,MAAA;;MAEA;MACA,MAAAG,GAAA,OAAAC,IAAA;MACA,MAAAC,SAAA,OAAAD,IAAA,MAAAhC,WAAA,CAAAkC,cAAA;MAEA,OAAAD,SAAA,GAAAF,GAAA;IACA;IAEA;IACAI,wBAAA;MACA,UAAAZ,cAAA,SAAAA,cAAA,CAAAa,MAAA;QACA;MACA;;MAEA;MACA,MAAAC,eAAA,QAAAd,cAAA,CAAAe,MAAA,CAAAC,MAAA,IACAA,MAAA,CAAAC,UAAA,WACA;;MAEA;MACA,MAAAC,cAAA;MACAJ,eAAA,CAAAK,OAAA,CAAAH,MAAA;QACA,MAAAI,SAAA,GAAAJ,MAAA,CAAAK,UAAA;QACA,KAAAH,cAAA,CAAAE,SAAA;UACAF,cAAA,CAAAE,SAAA;YACAA,SAAA,EAAAA,SAAA;YACAE,SAAA,EAAAN,MAAA,CAAAM,SAAA;YACAC,OAAA,EAAAP,MAAA,CAAAO,OAAA;YACAC,OAAA;UACA;QACA;QACAN,cAAA,CAAAE,SAAA,EAAAI,OAAA,CAAAC,IAAA,CAAAT,MAAA;MACA;;MAEA;MACA,OAAAU,MAAA,CAAAC,MAAA,CAAAT,cAAA,EAAAU,IAAA,EAAAC,CAAA,EAAAC,CAAA;QACA,WAAArB,IAAA,CAAAqB,CAAA,CAAAV,SAAA,QAAAX,IAAA,CAAAoB,CAAA,CAAAT,SAAA;MACA;IACA;EACA;EAEAW,QAAA;IACA,KAAAC,gBAAA;EACA;EAEAC,OAAA;IACA;IACA,MAAAD,iBAAA;MACA,KAAAxD,OAAA;MACA;QACA;QACA,MAAA0D,iBAAA,QAAAC,MAAA,CAAAC,MAAA,CAAAC,MAAA;QACA,MAAAC,IAAA,QAAAH,MAAA,CAAAC,MAAA,CAAAE,IAAA;QACA,IAAAC,QAAA;QAEA,IAAAL,iBAAA;UACAM,OAAA,CAAAC,GAAA,kBAAAP,iBAAA;;UAEA;UACA,MAAAQ,aAAA,QAAAP,MAAA,CAAAQ,KAAA,CAAAL,IAAA;;UAEA;UACA,IAAAI,aAAA;YACAF,OAAA,CAAAC,GAAA,kBAAAC,aAAA,EAAAR,iBAAA;;YAEA;YACAM,OAAA,CAAAC,GAAA,kBAAAP,iBAAA;;YAEA;YACAK,QAAA,SAAAzE,cAAA,CAAA8E,oBAAA,CAAAF,aAAA,EAAAR,iBAAA;UACA;YACA;YACAM,OAAA,CAAAC,GAAA,gBAAAP,iBAAA;;YAEA;YACA,MAAAW,SAAA,GAAAC,YAAA,CAAAC,OAAA;YAEA,IAAAF,SAAA;cACAL,OAAA,CAAAC,GAAA,0BAAAI,SAAA;;cAEA;cACAL,OAAA,CAAAC,GAAA,qBAAAI,SAAA,EAAAX,iBAAA;;cAEA;cACAK,QAAA,SAAAzE,cAAA,CAAA8E,oBAAA,CAAAC,SAAA,EAAAX,iBAAA;YACA;cACA;cACAK,QAAA,SAAAzE,cAAA,CAAA8E,oBAAA,CAAAV,iBAAA;YACA;UACA;QACA,gBAAA/D,OAAA;UACA;UACAoE,QAAA,SAAAzE,cAAA,CAAAkF,cAAA,CAAAV,IAAA;QACA;UACA;UACAC,QAAA,SAAAzE,cAAA,CAAA8E,oBAAA,CAAAN,IAAA;QACA;QAEA,IAAAC,QAAA,CAAAhE,IAAA,CAAA0E,OAAA;UACA,KAAAxE,WAAA,GAAA8D,QAAA,CAAAhE,IAAA,CAAAA,IAAA;UACAiE,OAAA,CAAAC,GAAA,kBAAAhE,WAAA;;UAEA;UACA,SAAAA,WAAA,CAAAyE,wBAAA;YACA,KAAAhD,sBAAA;YACA,KAAAD,sBAAA,QAAAxB,WAAA,CAAAyE,wBAAA;YACAV,OAAA,CAAAC,GAAA,4BAAAxC,sBAAA;UACA;;UAEA;UACA,MAAAkD,eAAA,QAAAhB,MAAA,CAAAQ,KAAA,CAAAS,KAAA,oBAAAjB,MAAA,CAAAQ,KAAA,CAAAU,WAAA;UACA,IAAAF,eAAA;YACA,KAAAlD,sBAAA,QAAAkC,MAAA,CAAAQ,KAAA,CAAAU,WAAA;YACAb,OAAA,CAAAC,GAAA,+BAAAxC,sBAAA;UACA;;UAEA;UACA,KAAAqD,SAAA;YACA,SAAAnB,MAAA,CAAAQ,KAAA,CAAAY,IAAA,oBAAAhD,SAAA;cACAiC,OAAA,CAAAC,GAAA;cACA,KAAAe,gBAAA;YACA;UACA;QACA;UACA,KAAAC,QAAA,CAAAC,KAAA,CAAAnB,QAAA,CAAAhE,IAAA,CAAAkB,OAAA,SAAAC,EAAA;UACA,KAAAjB,WAAA;QACA;MACA,SAAAiF,KAAA;QACAlB,OAAA,CAAAkB,KAAA,iCAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA,MAAAhE,EAAA;QACA,KAAAjB,WAAA;MACA;QACA,KAAAD,OAAA;MACA;IACA;IAEA;IACAmF,OAAA;MACAnB,OAAA,CAAAC,GAAA,8CAAAN,MAAA,CAAAQ,KAAA;;MAEA;MACA,MAAAQ,eAAA,QAAAhB,MAAA,CAAAQ,KAAA,CAAAS,KAAA,oBAAAnD,sBAAA;MAEA,IAAAkD,eAAA;QACA;QACAX,OAAA,CAAAC,GAAA,mDAAAxC,sBAAA;;QAEA;QACA,MAAA0C,KAAA;UACAiB,SAAA;UACAC,kBAAA,OAAApF,WAAA,CAAAoF;QACA;;QAEA;QACA,SAAA1B,MAAA,CAAAQ,KAAA,CAAAmB,WAAA;UACAnB,KAAA,CAAAmB,WAAA,QAAA3B,MAAA,CAAAQ,KAAA,CAAAmB,WAAA;QACA;;QAEA;QACA,SAAA3B,MAAA,CAAAQ,KAAA,CAAAoB,IAAA;UACApB,KAAA,CAAAoB,IAAA,QAAA5B,MAAA,CAAAQ,KAAA,CAAAoB,IAAA;QACA;QAEA,KAAAC,OAAA,CAAAvC,IAAA;UACAwC,IAAA,iCAAAhE,sBAAA;UACA0C,KAAA,EAAAA;QACA;MACA;QACA;QACAH,OAAA,CAAAC,GAAA;;QAEA;QACA,SAAAN,MAAA,CAAAQ,KAAA,CAAAoB,IAAA,sBAAA5B,MAAA,CAAAQ,KAAA,CAAAmB,WAAA,SAAA3B,MAAA,CAAAQ,KAAA,CAAAuB,eAAA;UACA;UACA,MAAAC,WAAA;UAEA,SAAAhC,MAAA,CAAAQ,KAAA,CAAAmB,WAAA;YACAK,WAAA,CAAAL,WAAA,QAAA3B,MAAA,CAAAQ,KAAA,CAAAmB,WAAA;UACA;UAEA,SAAA3B,MAAA,CAAAQ,KAAA,CAAAuB,eAAA;YACAC,WAAA,CAAAD,eAAA,QAAA/B,MAAA,CAAAQ,KAAA,CAAAuB,eAAA;UACA;;UAEA;UACAC,WAAA,CAAAC,SAAA;UAEA5B,OAAA,CAAAC,GAAA,yCAAA0B,WAAA;UAEA,KAAAH,OAAA,CAAAvC,IAAA;YACAwC,IAAA;YACAtB,KAAA,EAAAwB;UACA;QACA;UACA;UACA,KAAAH,OAAA,CAAAvC,IAAA;QACA;MACA;IACA;IAEA;IACA4C,eAAAC,QAAA;MACA,OAAAtG,UAAA,CAAAsG,QAAA;IACA;IAEA;IACAC,eAAAlE,MAAA;MACA,MAAAmE,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACA,OAAAL,SAAA,CAAAnE,MAAA;IACA;IAEA;IACAyE,cAAAzE,MAAA;MACA,MAAA0E,OAAA;QACAN,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACA,OAAAE,OAAA,CAAA1E,MAAA;IACA;IAEA;IACA2E,cAAA3E,MAAA;MACA,MAAAmE,SAAA;QACAC,SAAA,OAAA/E,EAAA;QACAgF,SAAA,OAAAhF,EAAA;QACAiF,SAAA,OAAAjF,EAAA;QACAkF,MAAA,OAAAlF,EAAA;QACAmF,OAAA,OAAAnF,EAAA;MACA;MACA,OAAA8E,SAAA,CAAAnE,MAAA,UAAAX,EAAA;IACA;IAIA;IACAuF,iBAAA;MACA,KAAAvG,mBAAA;IACA;IAEA;IACAwG,iBAAA;MACA,KAAAtG,mBAAA;IACA;IAEA;IACA,MAAAuG,kBAAA;MACA,KAAAxG,UAAA;MACA;QACA,MAAA4D,QAAA,SAAAzE,cAAA,CAAAqH,iBAAA,CACA,KAAA1G,WAAA,CAAA2G,gBAAA,EACA;UAAAvB,kBAAA,OAAApF,WAAA,CAAAoF;QAAA,CACA;QAEA,IAAAtB,QAAA,CAAAhE,IAAA,CAAA0E,OAAA;UACA,KAAAQ,QAAA,CAAAR,OAAA,MAAAvD,EAAA;UACA,KAAAhB,mBAAA;;UAEA;UACA2G,UAAA;YACA;YACA,MAAAlC,eAAA,QAAAhB,MAAA,CAAAQ,KAAA,CAAAS,KAAA,oBAAAnD,sBAAA;YAEA,IAAAkD,eAAA;cACA;cACA,MAAAR,KAAA;gBACAiB,SAAA;gBACAC,kBAAA,OAAApF,WAAA,CAAAoF;cACA;;cAEA;cACA,SAAA1B,MAAA,CAAAQ,KAAA,CAAAmB,WAAA;gBACAnB,KAAA,CAAAmB,WAAA,QAAA3B,MAAA,CAAAQ,KAAA,CAAAmB,WAAA;cACA;cAEA,KAAAE,OAAA,CAAAvC,IAAA;gBACAwC,IAAA,iCAAAhE,sBAAA;gBACA0C,KAAA,EAAAA;cACA;YACA;cACA;cACA,KAAAqB,OAAA,CAAAvC,IAAA;YACA;UACA;QACA;UACA,KAAAgC,QAAA,CAAAC,KAAA,CAAAnB,QAAA,CAAAhE,IAAA,CAAAkB,OAAA,SAAAC,EAAA;QACA;MACA,SAAAgE,KAAA;QACAlB,OAAA,CAAAkB,KAAA,kCAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA,MAAAhE,EAAA;MACA;QACA,KAAAf,UAAA;MACA;IACA;IAEA;IACA,MAAA2G,gBAAA;MACA,KAAAzG,SAAA;MACA;QACA;QACA;QACA,MAAA0D,QAAA,SAAAzE,cAAA,CAAAqH,iBAAA,CACA,KAAA1G,WAAA,CAAA2G,gBAAA,EACA;UACAvB,kBAAA,OAAApF,WAAA,CAAAoF,kBAAA;UACA0B,eAAA;QACA,CACA;QAEA,IAAAhD,QAAA,CAAAhE,IAAA,CAAA0E,OAAA;UACA,KAAAQ,QAAA,CAAAR,OAAA,MAAAvD,EAAA;UACA,KAAAd,mBAAA;;UAEA;UACAyG,UAAA;YACA;YACA,MAAAlC,eAAA,QAAAhB,MAAA,CAAAQ,KAAA,CAAAS,KAAA,oBAAAnD,sBAAA;YAEA,IAAAkD,eAAA;cACA;cACA,MAAAR,KAAA;gBACAiB,SAAA;gBACAC,kBAAA,OAAApF,WAAA,CAAAoF;cACA;;cAEA;cACA,SAAA1B,MAAA,CAAAQ,KAAA,CAAAmB,WAAA;gBACAnB,KAAA,CAAAmB,WAAA,QAAA3B,MAAA,CAAAQ,KAAA,CAAAmB,WAAA;cACA;cAEA,KAAAE,OAAA,CAAAvC,IAAA;gBACAwC,IAAA,iCAAAhE,sBAAA;gBACA0C,KAAA,EAAAA;cACA;YACA;cACA;cACA,KAAAqB,OAAA,CAAAvC,IAAA;YACA;UACA;QACA;UACA,KAAAgC,QAAA,CAAAC,KAAA,CAAAnB,QAAA,CAAAhE,IAAA,CAAAkB,OAAA,SAAAC,EAAA;QACA;MACA,SAAAgE,KAAA;QACAlB,OAAA,CAAAkB,KAAA,gCAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA,MAAAhE,EAAA;MACA;QACA,KAAAb,SAAA;MACA;IACA;IAEA;IACAgB,aAAA2F,IAAA;MACA,OAAAA,IAAA,CAAAC,OAAA,KAAAhF,IAAA,CAAAD,GAAA;IACA;IAEA;IACAgD,iBAAA;MACA;MACA,KAAAtE,UAAA;QACAC,aAAA,OAAAV,WAAA,CAAAkC,cAAA;QACAvB,WAAA,OAAAX,WAAA,CAAAiH,YAAA;QACArG,OAAA,OAAAZ,WAAA,CAAAY,OAAA;QACAC,SAAA,OAAAb,WAAA,CAAAkH,UAAA;MACA;;MAEA;MACA,KAAA7G,mBAAA;IACA;IAEA;IACA8G,kBAAA;MACA,MAAAlF,SAAA,OAAAD,IAAA,MAAAvB,UAAA,CAAAC,aAAA;MACA,MAAA0G,OAAA,OAAApF,IAAA,MAAAvB,UAAA,CAAAE,WAAA;MAEA,IAAAsB,SAAA,IAAAmF,OAAA;QACA,KAAApC,QAAA,CAAAC,KAAA,MAAAhE,EAAA;QACA;MACA;MAEA;IACA;IAEA;IACA,MAAAoG,sBAAA;MACA,UAAA5G,UAAA,CAAAC,aAAA,UAAAD,UAAA,CAAAE,WAAA;QACA;MACA;;MAEA;MACA,SAAAF,UAAA,CAAAC,aAAA,SAAAD,UAAA,CAAAE,WAAA;QACA,KAAAqE,QAAA,CAAAsC,OAAA,MAAArG,EAAA;QACA,KAAAV,YAAA;QACA;MACA;MAEA;QACA,MAAAgH,WAAA,QAAAvH,WAAA,CAAAwH,YAAA;QACA,MAAAC,SAAA,QAAAhH,UAAA,CAAAC,aAAA;QACA,MAAAgH,OAAA,QAAAjH,UAAA,CAAAE,WAAA;;QAEA;QACA,MAAAmD,QAAA,SAAAxE,YAAA,CAAAqI,eAAA,CAAAJ,WAAA,EAAAE,SAAA,EAAAC,OAAA;;QAEA;QACA,IAAA5D,QAAA,CAAAhE,IAAA,CAAA8H,mBAAA;UACA;UACA7D,OAAA,CAAAC,GAAA,eAAAF,QAAA,CAAAhE,IAAA,CAAA+H,SAAA;UACA,KAAAtH,YAAA,GAAAuD,QAAA,CAAAhE,IAAA,CAAA+H,SAAA,CAAAC,QAAA;QACA;UACA;UACA/D,OAAA,CAAAC,GAAA,aAAAF,QAAA,CAAAhE,IAAA,CAAA+H,SAAA;UACA,KAAAtH,YAAA,GAAAuD,QAAA,CAAAhE,IAAA,CAAA+H,SAAA,CAAAC,QAAA;QACA;QAEA,SAAAvH,YAAA;UACAwD,OAAA,CAAAC,GAAA,aAAAF,QAAA,CAAAhE,IAAA,CAAA+H,SAAA;;UAEA;UACA,IAAA/D,QAAA,CAAAhE,IAAA,CAAAiI,kBAAA,IAAAjE,QAAA,CAAAhE,IAAA,CAAAkI,gBAAA;YACA,KAAAxH,eAAA,sBAAAsD,QAAA,CAAAhE,IAAA,CAAAkI,gBAAA;UACA;YACA,KAAAxH,eAAA;UACA;UAEA,KAAAwE,QAAA,CAAAsC,OAAA,MAAArG,EAAA;QACA;UACA8C,OAAA,CAAAC,GAAA;UACA,KAAAxD,eAAA;QACA;MACA,SAAAyE,KAAA;QACAlB,OAAA,CAAAkB,KAAA,kCAAAA,KAAA;QACA,KAAA1E,YAAA;QACA,KAAAyE,QAAA,CAAAC,KAAA,MAAAhE,EAAA;MACA;IACA;IAEA;IACA,MAAAgH,YAAA;MACA,KAAA5G,oBAAA;MACA,KAAAC,cAAA;MAEA;QACA;QACA,MAAAwC,QAAA,SAAAzE,cAAA,CAAA6I,qBAAA,CACA,KAAAlI,WAAA,CAAA2G,gBAAA,EACA,KAAA3G,WAAA,CAAAoF,kBACA;QAEA,IAAAtB,QAAA,CAAAhE,IAAA,CAAA0E,OAAA;UACA,KAAAjD,cAAA,GAAAuC,QAAA,CAAAhE,IAAA,CAAAA,IAAA;QACA;UACA,KAAAkF,QAAA,CAAAC,KAAA,CAAAnB,QAAA,CAAAhE,IAAA,CAAAkB,OAAA,SAAAC,EAAA;UACA,KAAAM,cAAA;QACA;MACA,SAAA0D,KAAA;QACAlB,OAAA,CAAAkB,KAAA,6BAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA,MAAAhE,EAAA;QACA,KAAAM,cAAA;MACA;QACA,KAAAD,cAAA;MACA;IACA;IAEA;IACA6G,mBAAAC,MAAA;MACA,MAAAC,OAAA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,MAAA;IACA;IAEA;IACAE,qBAAAF,MAAA;MACA,MAAAG,SAAA;QACA,eAAAtH,EAAA;QACA,eAAAA,EAAA;QACA,eAAAA,EAAA;QACA,sBAAAA,EAAA;MACA;MACA,OAAAsH,SAAA,CAAAH,MAAA,KAAAA,MAAA;IACA;IAEA;IACAI,oBAAAC,SAAA;MACA,MAAAC,QAAA;QACA,uBAAAzH,EAAA;QACA,qBAAAA,EAAA;QACA,gBAAAA,EAAA;QACA,mBAAAA,EAAA;QACA,eAAAA,EAAA;MACA;MACA,OAAAyH,QAAA,CAAAD,SAAA,KAAAA,SAAA;IACA;IAEA;IACAE,mBAAAF,SAAA,EAAAG,KAAA;MACA,KAAAA,KAAA;MAEA,IAAAH,SAAA,yBAAAA,SAAA;QACA,OAAAlJ,UAAA,CAAAqJ,KAAA;MACA,WAAAH,SAAA;QACA,YAAAlC,aAAA,CAAAqC,KAAA;MACA;MAEA,OAAAA,KAAA;IACA;IAEA;IACAC,yBAAA;MACA,UAAA7I,WAAA,UAAAA,WAAA,CAAAyE,wBAAA;;MAEA;MACA,KAAAc,OAAA,CAAAvC,IAAA;QACAwC,IAAA,iCAAAxF,WAAA,CAAAyE,wBAAA;QACAP,KAAA;UACA4E,SAAA,OAAApJ,OAAA;UACA+F,eAAA,OAAAzF,WAAA,CAAA2G;QACA;MACA;IACA;IAEA;IACAoC,iBAAA;MACA,KAAAC,KAAA,CAAAvI,UAAA,CAAAwI,QAAA,OAAAC,KAAA;QACA,KAAAA,KAAA;;QAEA;QACA,UAAA/B,iBAAA;;QAEA;QACA,SAAA5G,YAAA;UACA,KAAAyE,QAAA,CAAAC,KAAA,MAAAhE,EAAA;UACA;QACA;QAEA,KAAAX,SAAA;QACA;UACA;UACA,MAAA6I,UAAA;YACAjH,cAAA,OAAAzB,UAAA,CAAAC,aAAA;YACAuG,YAAA,OAAAxG,UAAA,CAAAE,WAAA;YACAC,OAAA,OAAAH,UAAA,CAAAG,OAAA,IAAAwI,SAAA;YACAlC,UAAA,OAAAzG,UAAA,CAAAI,SAAA,IAAAuI,SAAA;YACAC,IAAA,OAAAC,KAAA,CAAAC;UACA;;UAEA;UACA,MAAAzF,QAAA,SAAAzE,cAAA,CAAAmK,iBAAA,CACA,KAAAxJ,WAAA,CAAA2G,gBAAA,EACAwC,UAAA,EACA,KAAAnJ,WAAA,CAAAoF,kBAAA;UACA;UAEA,IAAAtB,QAAA,CAAAhE,IAAA,CAAA0E,OAAA;YACA,KAAAQ,QAAA,CAAAR,OAAA,MAAAvD,EAAA;YACA,KAAAZ,mBAAA;YACA;YACA,WAAAkD,gBAAA;UACA;YACA,KAAAyB,QAAA,CAAAC,KAAA,CAAAnB,QAAA,CAAAhE,IAAA,CAAAkB,OAAA,SAAAC,EAAA;UACA;QACA,SAAAgE,KAAA;UACAlB,OAAA,CAAAkB,KAAA,kCAAAA,KAAA;UACA,KAAAD,QAAA,CAAAC,KAAA,MAAAhE,EAAA;QACA;UACA,KAAAX,SAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}