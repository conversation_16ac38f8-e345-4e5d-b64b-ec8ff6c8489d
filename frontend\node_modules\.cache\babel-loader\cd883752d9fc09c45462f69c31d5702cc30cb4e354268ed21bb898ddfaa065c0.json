{"ast": null, "code": "import axios from 'axios';\nimport store from '@/store';\nimport router from '@/router';\nimport { Message } from 'element-ui';\n\n// 获取当前主机名和端口\nconst hostname = window.location.hostname;\nconsole.log('Current hostname:', hostname);\n\n// 动态设置API的baseURL\nlet apiBaseURL = '';\nif (hostname === 'localhost' || hostname === '127.0.0.1') {\n  // 本地开发环境\n  apiBaseURL = 'http://localhost:8000';\n} else {\n  // 局域网/生产环境：使用当前主机IP，但端口改为后端端口\n  // 这里假设前端和后端在同一台服务器上，只是端口不同\n  apiBaseURL = `http://${hostname}:8001`;\n}\nconsole.log('Generated API URL:', apiBaseURL);\n\n// 创建axios实例\nconst service = axios.create({\n  baseURL: apiBaseURL,\n  timeout: 10000\n});\nconsole.log('API Base URL:', service.defaults.baseURL);\n\n// 请求拦截器\nservice.interceptors.request.use(config => {\n  // 从localStorage获取token\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  console.error('Request error:', error);\n  return Promise.reject(error);\n});\n\n// 响应拦截器\nservice.interceptors.response.use(response => {\n  return response.data;\n}, error => {\n  console.error('Response error:', error);\n  if (error.response) {\n    const {\n      status,\n      data\n    } = error.response;\n\n    // 处理不同的错误状态码\n    switch (status) {\n      case 400:\n        Message.error(data.detail || '请求参数错误');\n        break;\n      case 401:\n        Message.warning('您的会话已过期，请重新登录');\n        store.dispatch('logout');\n        router.push('/admin/login');\n        break;\n      case 403:\n        Message.error('没有权限访问该资源');\n        break;\n      case 404:\n        Message.error('请求的资源不存在');\n        break;\n      case 500:\n        Message.error('服务器内部错误');\n        break;\n      default:\n        Message.error(`请求失败: ${error.message}`);\n    }\n  } else {\n    Message.error('网络错误，请检查您的网络连接');\n  }\n  return Promise.reject(error);\n});\nexport default service;", "map": {"version": 3, "names": ["axios", "store", "router", "Message", "hostname", "window", "location", "console", "log", "apiBaseURL", "service", "create", "baseURL", "timeout", "defaults", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "error", "Promise", "reject", "response", "data", "status", "detail", "warning", "dispatch", "push", "message"], "sources": ["D:/Equipment-Reservation-System-main/frontend/src/utils/request.js"], "sourcesContent": ["import axios from 'axios'\nimport store from '@/store'\nimport router from '@/router'\nimport { Message } from 'element-ui'\n\n// 获取当前主机名和端口\nconst hostname = window.location.hostname\nconsole.log('Current hostname:', hostname)\n\n// 动态设置API的baseURL\nlet apiBaseURL = ''\nif (hostname === 'localhost' || hostname === '127.0.0.1') {\n  // 本地开发环境\n  apiBaseURL = 'http://localhost:8000'\n} else {\n  // 局域网/生产环境：使用当前主机IP，但端口改为后端端口\n  // 这里假设前端和后端在同一台服务器上，只是端口不同\n  apiBaseURL = `http://${hostname}:8001`\n}\n\nconsole.log('Generated API URL:', apiBaseURL)\n\n// 创建axios实例\nconst service = axios.create({\n  baseURL: apiBaseURL,\n  timeout: 10000\n})\n\nconsole.log('API Base URL:', service.defaults.baseURL)\n\n// 请求拦截器\nservice.interceptors.request.use(\n  config => {\n    // 从localStorage获取token\n    const token = localStorage.getItem('token')\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`\n    }\n    return config\n  },\n  error => {\n    console.error('Request error:', error)\n    return Promise.reject(error)\n  }\n)\n\n// 响应拦截器\nservice.interceptors.response.use(\n  response => {\n    return response.data\n  },\n  error => {\n    console.error('Response error:', error)\n\n    if (error.response) {\n      const { status, data } = error.response\n\n      // 处理不同的错误状态码\n      switch (status) {\n        case 400:\n          Message.error(data.detail || '请求参数错误')\n          break\n        case 401:\n          Message.warning('您的会话已过期，请重新登录')\n          store.dispatch('logout')\n          router.push('/admin/login')\n          break\n        case 403:\n          Message.error('没有权限访问该资源')\n          break\n        case 404:\n          Message.error('请求的资源不存在')\n          break\n        case 500:\n          Message.error('服务器内部错误')\n          break\n        default:\n          Message.error(`请求失败: ${error.message}`)\n      }\n    } else {\n      Message.error('网络错误，请检查您的网络连接')\n    }\n\n    return Promise.reject(error)\n  }\n)\n\nexport default service\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,OAAO,QAAQ,YAAY;;AAEpC;AACA,MAAMC,QAAQ,GAAGC,MAAM,CAACC,QAAQ,CAACF,QAAQ;AACzCG,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEJ,QAAQ,CAAC;;AAE1C;AACA,IAAIK,UAAU,GAAG,EAAE;AACnB,IAAIL,QAAQ,KAAK,WAAW,IAAIA,QAAQ,KAAK,WAAW,EAAE;EACxD;EACAK,UAAU,GAAG,uBAAuB;AACtC,CAAC,MAAM;EACL;EACA;EACAA,UAAU,GAAG,UAAUL,QAAQ,OAAO;AACxC;AAEAG,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,UAAU,CAAC;;AAE7C;AACA,MAAMC,OAAO,GAAGV,KAAK,CAACW,MAAM,CAAC;EAC3BC,OAAO,EAAEH,UAAU;EACnBI,OAAO,EAAE;AACX,CAAC,CAAC;AAEFN,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEE,OAAO,CAACI,QAAQ,CAACF,OAAO,CAAC;;AAEtD;AACAF,OAAO,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9BC,MAAM,IAAI;EACR;EACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACI,OAAO,CAACC,aAAa,GAAG,UAAUJ,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACDM,KAAK,IAAI;EACPjB,OAAO,CAACiB,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;EACtC,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAd,OAAO,CAACK,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC/BU,QAAQ,IAAI;EACV,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACDJ,KAAK,IAAI;EACPjB,OAAO,CAACiB,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;EAEvC,IAAIA,KAAK,CAACG,QAAQ,EAAE;IAClB,MAAM;MAAEE,MAAM;MAAED;IAAK,CAAC,GAAGJ,KAAK,CAACG,QAAQ;;IAEvC;IACA,QAAQE,MAAM;MACZ,KAAK,GAAG;QACN1B,OAAO,CAACqB,KAAK,CAACI,IAAI,CAACE,MAAM,IAAI,QAAQ,CAAC;QACtC;MACF,KAAK,GAAG;QACN3B,OAAO,CAAC4B,OAAO,CAAC,eAAe,CAAC;QAChC9B,KAAK,CAAC+B,QAAQ,CAAC,QAAQ,CAAC;QACxB9B,MAAM,CAAC+B,IAAI,CAAC,cAAc,CAAC;QAC3B;MACF,KAAK,GAAG;QACN9B,OAAO,CAACqB,KAAK,CAAC,WAAW,CAAC;QAC1B;MACF,KAAK,GAAG;QACNrB,OAAO,CAACqB,KAAK,CAAC,UAAU,CAAC;QACzB;MACF,KAAK,GAAG;QACNrB,OAAO,CAACqB,KAAK,CAAC,SAAS,CAAC;QACxB;MACF;QACErB,OAAO,CAACqB,KAAK,CAAC,SAASA,KAAK,CAACU,OAAO,EAAE,CAAC;IAC3C;EACF,CAAC,MAAM;IACL/B,OAAO,CAACqB,KAAK,CAAC,gBAAgB,CAAC;EACjC;EAEA,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAed,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}