{"ast": null, "code": "// import { Calendar } from '@fullcalendar/core';\nimport dayGridPlugin from '@fullcalendar/daygrid';\nimport timeGridPlugin from '@fullcalendar/timegrid';\nimport interactionPlugin from '@fullcalendar/interaction';\nimport FullCalendar from '@fullcalendar/vue';\nimport { formatDate } from '@/utils/date';\nimport reservationApi from '@/api/reservation';\nimport equipmentApi from '@/api/equipment';\nexport default {\n  name: 'CalendarView',\n  components: {\n    FullCalendar\n  },\n  data() {\n    return {\n      calendarOptions: {\n        plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin],\n        initialView: 'dayGridMonth',\n        headerToolbar: false,\n        // 我们使用自定义的头部控件\n        events: [],\n        // 将通过API加载\n        eventClick: this.handleEventClick,\n        eventDidMount: this.handleEventDidMount,\n        datesSet: this.handleDatesSet,\n        locale: this.$i18n.locale === 'zh-CN' ? 'zh-cn' : 'en',\n        firstDay: 1,\n        // 周一作为一周的第一天\n        allDaySlot: false,\n        // 不显示\"全天\"选项\n        slotMinTime: '00:00:00',\n        // 从0点开始\n        slotMaxTime: '24:00:00',\n        // 到24点结束\n        height: 'auto',\n        nowIndicator: true,\n        // 显示当前时间指示器\n        slotLabelFormat: {\n          hour: '2-digit',\n          minute: '2-digit',\n          hour12: false // 使用24小时制\n        },\n        slotEventOverlap: false,\n        // 禁止事件重叠\n        eventTimeFormat: {\n          // 统一时间格式\n          hour: '2-digit',\n          minute: '2-digit',\n          hour12: false\n        },\n        titleFormat: {\n          year: 'numeric',\n          month: 'long'\n        },\n        // 标题格式\n        views: {\n          dayGridMonth: {\n            dayHeaderFormat: {\n              weekday: 'long'\n            },\n            // 月视图只显示星期几\n            fixedWeekCount: false,\n            // 根据当月天数动态调整行数\n            showNonCurrentDates: false // 隐藏不属于当前月份的日期\n          },\n          timeGridWeek: {\n            dayHeaderFormat: {\n              weekday: 'long',\n              month: 'numeric',\n              day: 'numeric',\n              omitCommas: true\n            } // 周视图显示完整日期\n          },\n          timeGridDay: {\n            dayHeaderFormat: {\n              weekday: 'long',\n              month: 'numeric',\n              day: 'numeric',\n              omitCommas: true\n            },\n            // 日视图显示完整日期\n            slotEventOverlap: false,\n            // 日视图特别禁止事件重叠\n            eventMaxStack: 4,\n            // 最多显示4个事件，超过则显示\"+更多\"\n            moreLinkClick: 'popover' // 点击\"+更多\"时显示弹窗\n          }\n        },\n        locales: {\n          'zh-cn': {\n            week: {\n              dow: 1,\n              // 周一作为一周的第一天\n              doy: 4 // 一年中第一周必须包含1月4日\n            },\n            buttonText: {\n              today: '今天',\n              month: '月',\n              week: '周',\n              day: '日'\n            },\n            weekText: '周',\n            allDayText: '全天',\n            moreLinkText: '更多',\n            noEventsText: '没有事件'\n          },\n          'en': {\n            week: {\n              dow: 1,\n              // 周一作为一周的第一天\n              doy: 4 // 一年中第一周必须包含1月4日\n            },\n            buttonText: {\n              today: 'Today',\n              month: 'Month',\n              week: 'Week',\n              day: 'Day'\n            },\n            weekText: 'W',\n            allDayText: 'All day',\n            moreLinkText: 'more',\n            noEventsText: 'No events'\n          }\n        }\n      },\n      detailVisible: false,\n      selectedEvent: null,\n      loading: false,\n      currentViewTitle: '',\n      cancelDialogVisible: false,\n      cancelling: false,\n      cancelForm: {\n        reservationCode: ''\n      },\n      cancelRules: {\n        reservationCode: [{\n          required: true,\n          message: this.$t('reservation.codeOrContactRequired'),\n          trigger: 'blur'\n        }]\n      },\n      // 修改预约相关\n      modifyDialogVisible: false,\n      modifying: false,\n      modifyForm: {\n        reservationCode: ''\n      },\n      modifyRules: {\n        reservationCode: [{\n          required: true,\n          message: this.$t('reservation.codeOrContactRequired'),\n          trigger: 'blur'\n        }]\n      },\n      // 修改预约表单相关\n      modifyFormDialogVisible: false,\n      modifyFormSubmitting: false,\n      modifyTimeConflict: false,\n      modifyConflictMessage: '',\n      modifyConflictingReservations: [],\n      modifyTimeAvailabilityChecked: false,\n      modifyFormData: {\n        startDateTime: '',\n        endDateTime: '',\n        purpose: '',\n        userEmail: ''\n      },\n      modifyFormRules: {\n        startDateTime: [{\n          required: true,\n          message: this.$t('reservation.startTimeRequired'),\n          trigger: 'change'\n        }],\n        endDateTime: [{\n          required: true,\n          message: this.$t('reservation.endTimeRequired'),\n          trigger: 'change'\n        }],\n        userEmail: [{\n          required: true,\n          message: this.$t('reservation.emailRequired'),\n          trigger: 'blur'\n        }, {\n          type: 'email',\n          message: this.$t('reservation.emailFormat'),\n          trigger: 'blur'\n        }]\n      },\n      dateTimePickerOptions: {\n        disabledDate: time => {\n          return time.getTime() < Date.now() - 8.64e7; // 8.64e7是一天的毫秒数\n        }\n      },\n      // 设备筛选相关\n      selectedEquipment: null,\n      equipmentList: []\n    };\n  },\n  computed: {\n    // 修改时间冲突标题\n    modifyTimeConflictTitle() {\n      if (this.modifyConflictingReservations && this.modifyConflictingReservations.length > 0) {\n        return `所选时间段与 ${this.modifyConflictingReservations.length} 个预定冲突`;\n      } else if (this.modifyConflictMessage) {\n        return this.modifyConflictMessage;\n      } else {\n        return '所选时间段已被预定';\n      }\n    }\n  },\n  mounted() {\n    this.loadEvents();\n    this.loadEquipmentList();\n  },\n  beforeDestroy() {\n    // 清除资源\n  },\n  watch: {\n    // 监听语言变化\n    '$i18n.locale': {\n      handler(newLocale) {\n        if (this.$refs.fullCalendar) {\n          const calendarApi = this.$refs.fullCalendar.getApi();\n          calendarApi.setOption('locale', newLocale === 'zh-CN' ? 'zh-cn' : 'en');\n          this.currentViewTitle = calendarApi.view.title;\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    // 加载预约数据\n    async loadEvents() {\n      this.loading = true;\n      try {\n        const calendarApi = this.$refs.fullCalendar.getApi();\n        const start = this.formatDate(calendarApi.view.activeStart);\n        const end = this.formatDate(calendarApi.view.activeEnd);\n\n        // 构建请求参数\n        const params = {\n          start_date: start,\n          end_date: end\n        };\n\n        // 如果选择了设备，添加设备ID参数\n        if (this.selectedEquipment) {\n          params.equipment_id = this.selectedEquipment;\n        }\n        const response = await this.$http.get('/api/reservations/calendar', {\n          params\n        });\n        if (response.data.success) {\n          calendarApi.removeAllEvents();\n          calendarApi.addEventSource(response.data.events);\n        } else {\n          this.$message.error(response.data.message || this.$t('calendar.loadFailed'));\n        }\n      } catch (error) {\n        console.error('Failed to load calendar events:', error);\n        this.$message.error(this.$t('calendar.loadFailed'));\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 加载设备列表\n    async loadEquipmentList() {\n      try {\n        const response = await equipmentApi.getEquipments({\n          limit: 100\n        });\n        if (response.data && response.data.items) {\n          this.equipmentList = response.data.items;\n        }\n      } catch (error) {\n        console.error('Failed to load equipment list:', error);\n        this.$message.error(this.$t('error.serverError'));\n      }\n    },\n    // 处理设备选择变化\n    handleEquipmentChange() {\n      this.loadEvents();\n    },\n    // 处理日期范围变化\n    handleDatesSet() {\n      // 更新当前视图标题\n      const calendarApi = this.$refs.fullCalendar.getApi();\n      this.currentViewTitle = calendarApi.view.title;\n\n      // 加载事件数据\n      this.loadEvents();\n    },\n    // 处理事件点击\n    handleEventClick(info) {\n      this.selectedEvent = info.event;\n\n      // 确保 extendedProps 中包含 reservationCode 和 reservationNumber\n      if (info.event.extendedProps) {\n        // 如果没有 reservationCode，尝试从其他属性中获取\n        if (!info.event.extendedProps.reservationCode && info.event.extendedProps.reservation_code) {\n          info.event.extendedProps.reservationCode = info.event.extendedProps.reservation_code;\n        }\n\n        // 如果没有 reservationNumber，尝试从其他属性中获取\n        if (!info.event.extendedProps.reservationNumber && info.event.extendedProps.reservation_number) {\n          info.event.extendedProps.reservationNumber = info.event.extendedProps.reservation_number;\n        }\n      }\n      this.detailVisible = true;\n    },\n    // 处理事件渲染\n    handleEventDidMount(info) {\n      // 获取事件状态\n      const status = info.event.extendedProps.status;\n\n      // 根据状态设置事件颜色（适用于所有视图）\n      if (status) {\n        if (status === 'confirmed') {\n          info.el.style.backgroundColor = 'rgba(103, 194, 58, 0.7)'; // 已确认 - 半透明绿色\n          info.el.style.borderColor = 'rgba(103, 194, 58, 0.9)';\n        } else if (status === 'in_use') {\n          info.el.style.backgroundColor = 'rgba(64, 158, 255, 0.7)'; // 使用中 - 半透明蓝色\n          info.el.style.borderColor = 'rgba(64, 158, 255, 0.9)';\n        }\n      }\n\n      // 添加鼠标悬停工具提示\n      this.addEventTooltip(info);\n\n      // 为循环预约添加标记 - 适用于所有视图\n      if (info.event.extendedProps.isRecurring) {\n        // 月视图的处理方式\n        if (info.view.type === 'dayGridMonth') {\n          const recurringIcon = document.createElement('span');\n          recurringIcon.className = 'recurring-icon';\n          recurringIcon.innerHTML = '<i class=\"el-icon-refresh-right\"></i>';\n          const titleEl = info.el.querySelector('.fc-event-title');\n          if (titleEl) {\n            titleEl.appendChild(recurringIcon);\n          }\n        }\n        // 周视图和日视图的处理方式\n        else if (info.view.type === 'timeGridWeek' || info.view.type === 'timeGridDay') {\n          // 为事件添加循环图标标记\n          const recurringIcon = document.createElement('span');\n          recurringIcon.className = 'recurring-icon-timegrid';\n          recurringIcon.innerHTML = '<i class=\"el-icon-refresh-right\"></i>';\n\n          // 添加到自定义内容中\n          const eventContent = info.el.querySelector('.custom-event-content');\n          if (eventContent) {\n            eventContent.appendChild(recurringIcon);\n          } else {\n            // 如果还没有自定义内容，添加到事件主体\n            const mainContent = info.el.querySelector('.fc-event-main');\n            if (mainContent) {\n              mainContent.appendChild(recurringIcon);\n            }\n          }\n        }\n      }\n\n      // 在月视图中修改时间格式为24小时制\n      if (info.view.type === 'dayGridMonth') {\n        const timeEl = info.el.querySelector('.fc-event-time');\n        if (timeEl && info.event.start) {\n          // 使用24小时制格式化时间\n          const formattedTime = info.event.start.toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: false\n          });\n          timeEl.textContent = formattedTime;\n        }\n      }\n\n      // 在周视图和日视图中自定义事件内容\n      if (info.view.type === 'timeGridWeek' || info.view.type === 'timeGridDay') {\n        // 计算事件持续时间（分钟）\n        const start = info.event.start;\n        const end = info.event.end;\n        const durationMs = end - start;\n        const durationMinutes = Math.round(durationMs / (1000 * 60));\n\n        // 清除默认内容\n        const eventContent = info.el.querySelector('.fc-event-main');\n        if (eventContent) {\n          // 创建自定义内容\n          const customContent = document.createElement('div');\n          customContent.className = 'custom-event-content';\n\n          // 根据预约时长决定显示内容\n          if (durationMinutes <= 30) {\n            // 短时间预约：只显示设备名称+时间（一排）\n            const shortContent = document.createElement('div');\n            shortContent.className = 'event-short-content';\n\n            // 提取设备名称（取第一个单词或前两个单词）\n            const titleParts = info.event.title.split(' ');\n            let deviceName = titleParts[0];\n            if (titleParts.length > 1 && (titleParts[1] === 'Mic' || titleParts[1] === 'Speaker' || titleParts[1].startsWith('Speaker'))) {\n              deviceName = `${titleParts[0]} ${titleParts[1]}`;\n            }\n\n            // 格式化时间\n            const startTime = start ? start.toLocaleTimeString('zh-CN', {\n              hour: '2-digit',\n              minute: '2-digit',\n              hour12: false\n            }) : '';\n            const endTime = end ? end.toLocaleTimeString('zh-CN', {\n              hour: '2-digit',\n              minute: '2-digit',\n              hour12: false\n            }) : '';\n            shortContent.textContent = `${deviceName} ${startTime}-${endTime}`;\n            shortContent.style.fontWeight = 'bold';\n            shortContent.style.fontSize = '0.9em';\n            shortContent.style.fontc;\n            shortContent.style.textAlign = 'center';\n            shortContent.style.lineHeight = '1.2';\n            customContent.appendChild(shortContent);\n          } else {\n            // 长时间预约：保持原有的3排显示\n            // 设备名称\n            const title = document.createElement('div');\n            title.className = 'event-title';\n            title.textContent = info.event.title;\n            customContent.appendChild(title);\n\n            // 使用人\n            const user = document.createElement('div');\n            user.className = 'event-user';\n            user.textContent = info.event.extendedProps.userName || '';\n            customContent.appendChild(user);\n\n            // 时间\n            const time = document.createElement('div');\n            time.className = 'event-time';\n            const startTime = start ? start.toLocaleTimeString('zh-CN', {\n              hour: '2-digit',\n              minute: '2-digit',\n              hour12: false\n            }) : '';\n            const endTime = end ? end.toLocaleTimeString('zh-CN', {\n              hour: '2-digit',\n              minute: '2-digit',\n              hour12: false\n            }) : '';\n            time.textContent = `${startTime}-${endTime}`;\n            customContent.appendChild(time);\n          }\n\n          // 替换内容\n          eventContent.innerHTML = '';\n          eventContent.appendChild(customContent);\n\n          // 为循环预约添加图标\n          if (info.event.extendedProps.isRecurring) {\n            const recurringIcon = document.createElement('span');\n            recurringIcon.className = 'recurring-icon-timegrid';\n            recurringIcon.innerHTML = '<i class=\"el-icon-refresh-right\"></i>';\n            eventContent.appendChild(recurringIcon);\n          }\n        }\n      }\n    },\n    // 切换视图\n    changeView(viewName) {\n      const calendarApi = this.$refs.fullCalendar.getApi();\n      calendarApi.changeView(viewName);\n      // 更新当前视图类型\n      this.calendarOptions.initialView = viewName;\n    },\n    // 跳转到今天\n    today() {\n      const calendarApi = this.$refs.fullCalendar.getApi();\n      calendarApi.today();\n    },\n    // 上一个时间段\n    prev() {\n      const calendarApi = this.$refs.fullCalendar.getApi();\n      calendarApi.prev();\n    },\n    // 下一个时间段\n    next() {\n      const calendarApi = this.$refs.fullCalendar.getApi();\n      calendarApi.next();\n    },\n    // 获取状态标签类型\n    getStatusTagType(status) {\n      const statusMap = {\n        'confirmed': 'success',\n        'in_use': 'primary',\n        'cancelled': 'info',\n        'expired': 'danger'\n      };\n      return statusMap[status] || 'info';\n    },\n    // 格式化日期\n    formatDate(date) {\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\n    },\n    // 格式化日期时间\n    formatDateTime(date) {\n      // 不进行时区转换（第三个参数设为false），避免时间多加8小时\n      return formatDate(date, 'YYYY-MM-DD HH:mm', false);\n    },\n    // 格式化短时间（只显示小时和分钟）\n    formatShortTime(date) {\n      if (!date) return '';\n      return date.toLocaleTimeString('zh-CN', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n      });\n    },\n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        'confirmed': this.$t('reservation.confirmed'),\n        'in_use': this.$t('reservation.inUse'),\n        'cancelled': this.$t('reservation.cancelled'),\n        'expired': this.$t('reservation.expired')\n      };\n      return statusMap[status] || status;\n    },\n    // 添加事件工具提示\n    addEventTooltip(info) {\n      // 获取事件信息\n      const event = info.event;\n      const props = event.extendedProps;\n\n      // 计算事件持续时间（分钟）\n      const start = event.start;\n      const end = event.end;\n      const durationMs = end - start;\n      const durationMinutes = Math.round(durationMs / (1000 * 60));\n\n      // 获取当前视图类型\n      const calendarApi = this.$refs.fullCalendar.getApi();\n      const viewType = calendarApi.view.type;\n\n      // 获取事件元素\n      const titleEl = info.el.querySelector('.fc-event-title');\n      const timeEl = info.el.querySelector('.fc-event-time');\n      const mainEl = info.el.querySelector('.fc-event-main');\n\n      // 根据视图类型处理事件显示\n      if (viewType === 'dayGridMonth') {\n        // 月视图：所有预约显示相同，显示设备名称+开始和结束时间\n        if (titleEl) {\n          // 提取完整设备名称（通常是前两个单词，如\"M2F Mic\"）\n          const titleParts = event.title.split(' ');\n          let deviceName = titleParts[0]; // 至少包含第一个单词\n\n          // 如果第二个单词是\"Mic\"或其他设备类型标识，也包含它\n          if (titleParts.length > 1 && (titleParts[1] === 'Mic' || titleParts[1] === 'Speaker' || titleParts[1].startsWith('Speaker'))) {\n            deviceName = `${titleParts[0]} ${titleParts[1]}`;\n          }\n\n          // 格式化时间\n          const startTime = this.formatShortTime(start);\n          const endTime = this.formatShortTime(end);\n\n          // 设置标题为\"设备名称 开始-结束\"\n          titleEl.textContent = `${deviceName}     ${startTime}-${endTime}`;\n\n          // 完全移除时间元素，防止重复显示时间\n          if (timeEl) {\n            timeEl.remove(); // 彻底移除时间元素，而不仅仅是隐藏\n          }\n        }\n      } else if (viewType === 'timeGridWeek' || viewType === 'timeGridDay') {\n        // 周视图和日视图：短时间预约特殊处理\n        if (durationMinutes <= 30) {\n          // 添加短时间预约的类\n          info.el.classList.add('very-short-duration-event');\n\n          // 只显示设备名称\n          if (titleEl) {\n            const deviceName = event.title.split(' ')[0];\n            titleEl.textContent = deviceName;\n          }\n\n          // 调整主容器样式\n          if (mainEl) {\n            mainEl.style.display = 'flex';\n            mainEl.style.alignItems = 'center';\n            mainEl.style.justifyContent = 'center';\n            mainEl.style.padding = '2px';\n\n            // 日视图和周视图的不同布局\n            if (viewType === 'timeGridDay') {\n              // 日视图：水平排列\n              mainEl.style.flexDirection = 'row';\n              if (timeEl) {\n                timeEl.style.marginRight = '4px';\n              }\n            } else {\n              // 周视图：垂直排列\n              mainEl.style.flexDirection = 'column';\n              if (timeEl) {\n                timeEl.style.marginBottom = '2px';\n              }\n            }\n          }\n\n          // 调整字体样式\n          if (titleEl) {\n            titleEl.style.fontWeight = 'bold';\n            titleEl.style.fontSize = '0.9em';\n          }\n          if (timeEl) {\n            timeEl.style.fontWeight = 'bold';\n            timeEl.style.fontSize = '0.9em';\n          }\n        }\n      }\n\n      // 设置title属性（原生浏览器工具提示）\n      const tooltipText = `${event.title}\\n${props.userName || ''}\\n${this.formatDateTime(start)} - ${this.formatDateTime(end)}\\n${this.getStatusText(props.status)}\\n${props.reservationNumber}`;\n      info.el.setAttribute('title', tooltipText);\n    },\n    // 显示取消预约对话框\n    showCancelDialog() {\n      this.cancelForm.reservationCode = '';\n      this.cancelDialogVisible = true;\n    },\n    // 检查预约是否已开始\n    isReservationStarted(event) {\n      const now = new Date();\n      const startTime = new Date(event.start);\n      return startTime <= now;\n    },\n    // 显示修改对话框\n    showModifyDialog() {\n      this.modifyForm.reservationCode = '';\n      this.modifyDialogVisible = true;\n    },\n    // 确认修改预约\n    async confirmModifyDialog() {\n      try {\n        // 验证表单\n        await this.$refs.modifyForm.validate();\n\n        // 检查输入的预约码是否与当前预约码匹配\n        if (this.modifyForm.reservationCode !== this.selectedEvent.extendedProps.reservationCode) {\n          this.$message.error(this.$t('reservation.checkCodeAndContact'));\n          return;\n        }\n\n        // 关闭验证对话框\n        this.modifyDialogVisible = false;\n\n        // 初始化修改表单数据\n        let startDateTime, endDateTime;\n        try {\n          startDateTime = new Date(this.selectedEvent.start);\n          endDateTime = new Date(this.selectedEvent.end);\n\n          // 检查日期是否有效\n          if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {\n            throw new Error('Invalid date');\n          }\n        } catch (e) {\n          console.error('Error creating date objects:', e);\n          // 使用当前时间作为默认值\n          startDateTime = new Date();\n          endDateTime = new Date();\n          endDateTime.setHours(endDateTime.getHours() + 1); // 结束时间默认为当前时间加1小时\n        }\n\n        // 手动格式化日期时间，确保格式与el-date-picker的value-format属性匹配\n        const formatDateTimeForPicker = date => {\n          if (!date || isNaN(date.getTime())) return null;\n          const year = date.getFullYear();\n          const month = String(date.getMonth() + 1).padStart(2, '0');\n          const day = String(date.getDate()).padStart(2, '0');\n          const hours = String(date.getHours()).padStart(2, '0');\n          const minutes = String(date.getMinutes()).padStart(2, '0');\n          const seconds = String(date.getSeconds()).padStart(2, '0');\n          return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;\n        };\n        this.modifyFormData = {\n          startDateTime: formatDateTimeForPicker(startDateTime),\n          endDateTime: formatDateTimeForPicker(endDateTime),\n          purpose: this.selectedEvent.extendedProps.purpose || '',\n          userEmail: this.selectedEvent.extendedProps.userEmail || ''\n        };\n\n        // 显示修改表单对话框\n        this.modifyFormDialogVisible = true;\n      } catch (error) {\n        console.error('Failed to validate modification form:', error);\n        this.$message.error(this.$t('error.serverError'));\n      } finally {\n        this.modifying = false;\n      }\n    },\n    // 验证时间范围\n    validateTimeRange() {\n      const startTime = new Date(this.modifyFormData.startDateTime);\n      const endTime = new Date(this.modifyFormData.endDateTime);\n      if (startTime >= endTime) {\n        this.$message.error(this.$t('reservation.invalidTime'));\n        return false;\n      }\n      return true;\n    },\n    // 检查修改时间可用性\n    async checkModifyTimeAvailability() {\n      if (!this.modifyFormData.startDateTime || !this.modifyFormData.endDateTime) {\n        this.modifyTimeAvailabilityChecked = false;\n        return;\n      }\n\n      // 添加更严格的验证\n      if (this.modifyFormData.startDateTime >= this.modifyFormData.endDateTime) {\n        this.modifyTimeConflict = true;\n        this.modifyTimeAvailabilityChecked = false;\n        return;\n      }\n      try {\n        const equipmentId = this.selectedEvent.extendedProps.equipmentId;\n        const startDate = this.modifyFormData.startDateTime;\n        const endDate = this.modifyFormData.endDateTime;\n\n        // 调用API检查时间可用性，排除当前预定\n        const excludeId = this.selectedEvent.id; // 使用事件的id字段，而不是extendedProps.reservationId\n        const params = {\n          start_date: startDate,\n          end_date: endDate\n        };\n\n        // 只有当excludeId存在且不为null/undefined时才添加参数\n        if (excludeId != null && excludeId !== undefined) {\n          params.exclude_reservation_id = excludeId;\n        }\n        const response = await this.$http.get(`/api/equipment/${equipmentId}/availability`, {\n          params\n        });\n\n        // 检查是否有冲突 - 处理API响应格式\n        if (response.data.specific_time_check) {\n          // 如果是具体时间段检查\n          console.log('具体时间段检查结果:', response.data.available);\n          this.modifyTimeConflict = response.data.available.includes(false);\n        } else {\n          // 如果是按日期检查\n          console.log('按日期检查结果:', response.data.available);\n          this.modifyTimeConflict = response.data.available.includes(false);\n        }\n\n        // 设置冲突信息\n        if (this.modifyTimeConflict) {\n          console.log('检测到时间冲突:', response.data.available);\n\n          // 获取冲突的预定信息\n          this.modifyConflictingReservations = response.data.conflicting_reservations || [];\n\n          // 检查是否是因为达到最大同时预定数量\n          if (response.data.allow_simultaneous && response.data.max_simultaneous > 1) {\n            this.modifyConflictMessage = `该时间段已达到最大同时预定数量(${response.data.max_simultaneous})`;\n          } else {\n            this.modifyConflictMessage = '';\n          }\n        } else {\n          console.log('时间段可用');\n          this.modifyConflictMessage = '';\n          this.modifyConflictingReservations = [];\n        }\n        this.modifyTimeAvailabilityChecked = true;\n      } catch (error) {\n        console.error('Failed to check availability:', error);\n        this.modifyTimeConflict = true;\n        this.modifyTimeAvailabilityChecked = false;\n        this.modifyConflictingReservations = [];\n        this.$message.error(this.$t('common.error'));\n      }\n    },\n    // 提交修改表单\n    async submitModifyForm() {\n      try {\n        // 验证表单\n        await this.$refs.modifyFormRef.validate();\n\n        // 验证时间范围\n        if (!this.validateTimeRange()) return;\n\n        // 检查时间冲突\n        if (this.modifyTimeConflict) {\n          this.$message.error(this.$t('reservation.timeConflict'));\n          return;\n        }\n        this.modifyFormSubmitting = true;\n\n        // 构建更新数据\n        const updateData = {\n          start_datetime: this.modifyFormData.startDateTime,\n          end_datetime: this.modifyFormData.endDateTime,\n          purpose: this.modifyFormData.purpose || undefined,\n          user_email: this.modifyFormData.userEmail || undefined,\n          lang: this.$i18n.locale\n        };\n\n        // 调用更新API - 传递预约序号以确保修改正确的子预约\n        const response = await reservationApi.updateReservation(this.selectedEvent.extendedProps.reservationCode, updateData, this.selectedEvent.extendedProps.reservationNumber // 传递预约序号\n        );\n        if (response.data && response.data.success) {\n          this.$message.success(this.$t('reservation.updateSuccess'));\n          this.modifyFormDialogVisible = false;\n\n          // 关闭预约详情弹窗\n          this.detailVisible = false;\n\n          // 重新加载日历事件\n          this.loadEvents();\n        } else {\n          const errorMessage = response.data && response.data.message ? response.data.message : this.$t('reservation.updateFailed');\n          this.$message.error(errorMessage);\n        }\n      } catch (error) {\n        console.error('Failed to update reservation:', error);\n        this.$message.error(this.$t('error.serverError'));\n      } finally {\n        this.modifyFormSubmitting = false;\n      }\n    },\n    // 取消预约\n    async cancelReservation() {\n      try {\n        // 验证表单\n        await this.$refs.cancelForm.validate();\n\n        // 检查输入的预约码是否与当前预约码匹配\n        if (this.cancelForm.reservationCode !== this.selectedEvent.extendedProps.reservationCode) {\n          this.$message.error(this.$t('reservation.checkCodeAndContact'));\n          return;\n        }\n        this.cancelling = true;\n\n        // 准备请求数据\n        const data = {\n          reservation_number: this.selectedEvent.extendedProps.reservationNumber || null,\n          lang: this.$i18n.locale\n        };\n\n        // 调用取消预约API\n        const response = await reservationApi.cancelReservation(this.selectedEvent.extendedProps.reservationCode, data);\n        if (response.data.success) {\n          this.$message.success(this.$t('reservation.cancelSuccess'));\n          this.cancelDialogVisible = false;\n\n          // 关闭预约详情弹窗\n          this.detailVisible = false;\n\n          // 重新加载日历事件\n          this.loadEvents();\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.cancelFailed'));\n        }\n      } catch (error) {\n        console.error('Failed to cancel reservation:', error);\n        this.$message.error(this.$t('error.serverError'));\n      } finally {\n        this.cancelling = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["dayGridPlugin", "timeGridPlugin", "interactionPlugin", "FullCalendar", "formatDate", "reservationApi", "equipmentApi", "name", "components", "data", "calendarOptions", "plugins", "initialView", "headerToolbar", "events", "eventClick", "handleEventClick", "eventDidMount", "handleEventDidMount", "datesSet", "handleDatesSet", "locale", "$i18n", "firstDay", "allDaySlot", "slotMinTime", "slotMaxTime", "height", "nowIndicator", "slotLabelFormat", "hour", "minute", "hour12", "slotEventOverlap", "eventTimeFormat", "titleFormat", "year", "month", "views", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dayHeaderFormat", "weekday", "fixedWeekCount", "showNonCurrentDates", "timeGridWeek", "day", "omit<PERSON>om<PERSON>", "timeGridDay", "eventMaxStack", "moreLinkClick", "locales", "week", "dow", "doy", "buttonText", "today", "weekText", "allDayText", "moreLinkText", "noEventsText", "detailVisible", "selectedEvent", "loading", "currentViewTitle", "cancelDialogVisible", "cancelling", "cancelForm", "reservationCode", "cancelRules", "required", "message", "$t", "trigger", "modifyDialogVisible", "modifying", "modifyForm", "modifyRules", "modifyFormDialogVisible", "modifyFormSubmitting", "modifyTimeConflict", "modifyConflictMessage", "modifyConflictingReservations", "modifyTimeAvailabilityChecked", "modifyFormData", "startDateTime", "endDateTime", "purpose", "userEmail", "modifyFormRules", "type", "dateTimePickerOptions", "disabledDate", "time", "getTime", "Date", "now", "selectedEquipment", "equipmentList", "computed", "modifyTimeConflictTitle", "length", "mounted", "loadEvents", "loadEquipmentList", "<PERSON><PERSON><PERSON><PERSON>", "watch", "handler", "newLocale", "$refs", "fullCalendar", "calendarApi", "getApi", "setOption", "view", "title", "immediate", "methods", "start", "activeStart", "end", "activeEnd", "params", "start_date", "end_date", "equipment_id", "response", "$http", "get", "success", "removeAllEvents", "addEventSource", "$message", "error", "console", "getEquipments", "limit", "items", "handleEquipmentChange", "info", "event", "extendedProps", "reservation_code", "reservationNumber", "reservation_number", "status", "el", "style", "backgroundColor", "borderColor", "addEventTooltip", "isRecurring", "recurringIcon", "document", "createElement", "className", "innerHTML", "titleEl", "querySelector", "append<PERSON><PERSON><PERSON>", "eventContent", "mainContent", "timeEl", "formattedTime", "toLocaleTimeString", "textContent", "durationMs", "durationMinutes", "Math", "round", "customContent", "shortContent", "titleParts", "split", "deviceName", "startsWith", "startTime", "endTime", "fontWeight", "fontSize", "fontc", "textAlign", "lineHeight", "user", "userName", "changeView", "viewName", "prev", "next", "getStatusTagType", "statusMap", "date", "getFullYear", "String", "getMonth", "padStart", "getDate", "formatDateTime", "formatShortTime", "getStatusText", "props", "viewType", "mainEl", "remove", "classList", "add", "display", "alignItems", "justifyContent", "padding", "flexDirection", "marginRight", "marginBottom", "tooltipText", "setAttribute", "showCancelDialog", "isReservationStarted", "showModifyDialog", "confirmModifyDialog", "validate", "isNaN", "Error", "e", "setHours", "getHours", "formatDateTimeForPicker", "hours", "minutes", "getMinutes", "seconds", "getSeconds", "validateTimeRange", "checkModifyTimeAvailability", "equipmentId", "startDate", "endDate", "excludeId", "id", "undefined", "exclude_reservation_id", "specific_time_check", "log", "available", "includes", "conflicting_reservations", "allow_simultaneous", "max_simultaneous", "submitModifyForm", "modifyFormRef", "updateData", "start_datetime", "end_datetime", "user_email", "lang", "updateReservation", "errorMessage", "cancelReservation"], "sources": ["src/views/calendar/CalendarView.vue"], "sourcesContent": ["<template>\n  <div class=\"calendar-view\">\n    <div class=\"calendar-header\">\n      <el-row type=\"flex\" justify=\"space-between\" align=\"middle\">\n        <el-col :span=\"8\">\n          <h1>{{ $t('calendar.title') }}</h1>\n          <h2 class=\"calendar-current-date\">{{ currentViewTitle }}</h2>\n        </el-col>\n        <el-col :span=\"16\">\n          <div class=\"calendar-controls\">\n            <el-button-group>\n              <el-button\n                size=\"small\"\n                @click=\"changeView('dayGridMonth')\"\n                :type=\"calendarOptions.initialView === 'dayGridMonth' ? 'primary' : ''\"\n              >{{ $t('calendar.month') }}</el-button>\n              <el-button\n                size=\"small\"\n                @click=\"changeView('timeGridWeek')\"\n                :type=\"calendarOptions.initialView === 'timeGridWeek' ? 'primary' : ''\"\n              >{{ $t('calendar.week') }}</el-button>\n              <el-button\n                size=\"small\"\n                @click=\"changeView('timeGridDay')\"\n                :type=\"calendarOptions.initialView === 'timeGridDay' ? 'primary' : ''\"\n              >{{ $t('calendar.day') }}</el-button>\n            </el-button-group>\n            <el-button size=\"small\" @click=\"today\">{{ $t('calendar.today') }}</el-button>\n            <el-button-group>\n              <el-button size=\"small\" icon=\"el-icon-arrow-left\" @click=\"prev\"></el-button>\n              <el-button size=\"small\" icon=\"el-icon-arrow-right\" @click=\"next\"></el-button>\n            </el-button-group>\n          </div>\n        </el-col>\n      </el-row>\n\n      <!-- 设备筛选器 -->\n      <el-row :gutter=\"20\" class=\"filter-row\">\n        <el-col :span=\"24\">\n          <div class=\"equipment-filter\">\n            <el-select\n              v-model=\"selectedEquipment\"\n              :placeholder=\"$t('calendar.selectEquipment')\"\n              clearable\n              @change=\"handleEquipmentChange\"\n              style=\"width: 300px;\"\n            >\n              <el-option\n                v-for=\"item in equipmentList\"\n                :key=\"item.id\"\n                :label=\"item.name\"\n                :value=\"item.id\"\n              ></el-option>\n            </el-select>\n          </div>\n        </el-col>\n      </el-row>\n\n      <!-- 预约状态提示 -->\n      <el-row :gutter=\"20\" class=\"status-legend-row\">\n        <el-col :span=\"24\">\n          <div class=\"status-legend\">\n            <el-alert\n              type=\"primary\"\n              :closable=\"false\"\n\n            >\n              <div class=\"status-legend-content\">\n                <div class=\"status-colors\">\n                  <span class=\"status-item\">\n                    <span class=\"status-color confirmed-color\"></span>\n                    {{ $t('calendar.confirmedStatus') }}\n                  </span>\n                  <span class=\"status-item\">\n                    <span class=\"status-color inuse-color\"></span>\n                    {{ $t('calendar.inUseStatus') }}\n                  </span>\n                </div>\n                <div class=\"cancel-tip-container\">\n                  <span class=\"status-item cancel-tip\">\n                    <i class=\"el-icon-info\"></i>\n                    {{ $t('calendar.cancelTip') }}\n                  </span>\n                </div>\n                <div class=\"update-tip-container\">\n                  <span class=\"status-item update-tip\">\n                    <i class=\"el-icon-info\"></i>\n                    {{ $t('calendar.updateTip') }}\n                  </span>\n                </div>\n              </div>\n            </el-alert>\n          </div>\n        </el-col>\n      </el-row>\n    </div>\n\n    <FullCalendar\n      ref=\"fullCalendar\"\n      :options=\"calendarOptions\"\n    />\n\n    <!-- 预约详情弹窗 -->\n    <el-dialog\n      :visible.sync=\"detailVisible\"\n      width=\"400px\"\n      :title=\"$t('calendar.reservationInfo')\"\n      :modal-append-to-body=\"false\"\n      :close-on-click-modal=\"true\"\n      class=\"calendar-detail-dialog\"\n    >\n      <div v-if=\"selectedEvent\" class=\"event-detail-card\">\n        <div class=\"event-header\" :class=\"'status-' + selectedEvent.extendedProps.status\">\n          <h3>{{ selectedEvent.title }}</h3>\n          <el-tag size=\"small\" :type=\"getStatusTagType(selectedEvent.extendedProps.status)\">\n            {{ getStatusText(selectedEvent.extendedProps.status) }}\n          </el-tag>\n        </div>\n\n        <div class=\"event-info\">\n          <div class=\"info-item time-info\">\n            <i class=\"el-icon-time\"></i>\n            <span class=\"time-display\">{{ formatDateTime(selectedEvent.start) }} - {{ formatDateTime(selectedEvent.end) }}</span>\n          </div>\n\n          <div class=\"info-item\">\n            <i class=\"el-icon-user\"></i>\n            <span>{{ selectedEvent.extendedProps.userName }} ({{ selectedEvent.extendedProps.userDepartment }})</span>\n          </div>\n\n          <div class=\"info-item\" v-if=\"selectedEvent.extendedProps.userEmail\">\n            <i class=\"el-icon-message\"></i>\n            <span>{{ selectedEvent.extendedProps.userEmail }}</span>\n          </div>\n        </div>\n\n        <!-- 循环预约提示 -->\n        <div v-if=\"selectedEvent.extendedProps.isRecurring\" class=\"recurring-notice\">\n          <el-alert\n            :title=\"$t('reservation.partOfRecurringReservation')\"\n            type=\"info\"\n            :closable=\"false\">\n          </el-alert>\n        </div>\n\n        <!-- 取消预约/提前归还按钮 -->\n        <div class=\"action-buttons\">\n          <!-- 添加修改按钮，只在预约状态为 confirmed 且未开始时显示 -->\n          <el-button\n            v-if=\"selectedEvent.extendedProps.status === 'confirmed' && !isReservationStarted(selectedEvent)\"\n            type=\"primary\"\n            @click=\"showModifyDialog\"\n            style=\"margin-right: 10px;\"\n          >\n            {{ $t('reservation.modifyReservation') }}\n          </el-button>\n\n          <el-button\n            type=\"danger\"\n            @click=\"showCancelDialog\"\n          >\n            {{ selectedEvent.extendedProps.status === 'in_use' ? $t('reservation.earlyReturn') : $t('reservation.cancelReservation') }}\n          </el-button>\n        </div>\n      </div>\n    </el-dialog>\n\n    <!-- 取消预约/提前归还对话框 -->\n    <el-dialog\n      :title=\"selectedEvent && selectedEvent.extendedProps.status === 'in_use' ? $t('reservation.earlyReturn') : $t('reservation.cancelReservation')\"\n      :visible.sync=\"cancelDialogVisible\"\n      width=\"400px\"\n      :modal-append-to-body=\"false\"\n      :close-on-click-modal=\"true\"\n      class=\"calendar-cancel-dialog\"\n    >\n      <div class=\"cancel-content\">\n        <p>{{ selectedEvent && selectedEvent.extendedProps.status === 'in_use' ? $t('reservation.confirmEarlyReturn') : $t('reservation.confirmCancel') }}</p>\n\n        <el-form ref=\"cancelForm\" :model=\"cancelForm\" :rules=\"cancelRules\" label-position=\"top\">\n          <el-form-item :label=\"$t('reservation.code')\" prop=\"reservationCode\">\n            <el-input\n              v-model=\"cancelForm.reservationCode\"\n              :placeholder=\"$t('reservation.queryPlaceholder')\"\n            ></el-input>\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"cancelDialogVisible = false\">{{ $t('common.cancel') }}</el-button>\n        <el-button type=\"danger\" :loading=\"cancelling\" @click=\"cancelReservation\">{{ $t('common.confirm') }}</el-button>\n      </span>\n    </el-dialog>\n\n    <!-- 修改预约对话框 -->\n    <el-dialog\n      :title=\"$t('reservation.modifyReservation')\"\n      :visible.sync=\"modifyDialogVisible\"\n      width=\"400px\"\n      :modal-append-to-body=\"false\"\n      :close-on-click-modal=\"true\"\n      class=\"calendar-modify-dialog\"\n    >\n      <div class=\"cancel-content\">\n        <p>{{ $t('reservation.modifyReservation') }}</p>\n\n        <el-form ref=\"modifyForm\" :model=\"modifyForm\" :rules=\"modifyRules\" label-position=\"top\">\n          <el-form-item :label=\"$t('reservation.code')\" prop=\"reservationCode\">\n            <el-input\n              v-model=\"modifyForm.reservationCode\"\n              :placeholder=\"$t('reservation.queryPlaceholder')\"\n            ></el-input>\n          </el-form-item>\n        </el-form>\n      </div>\n\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"modifyDialogVisible = false\">{{ $t('common.cancel') }}</el-button>\n        <el-button type=\"primary\" :loading=\"modifying\" @click=\"confirmModifyDialog\">{{ $t('common.confirm') }}</el-button>\n      </span>\n    </el-dialog>\n\n    <!-- 修改预约表单对话框 -->\n    <el-dialog\n      :title=\"$t('reservation.modifyReservation')\"\n      :visible.sync=\"modifyFormDialogVisible\"\n      width=\"600px\"\n      :modal-append-to-body=\"false\"\n      :close-on-click-modal=\"true\"\n      class=\"calendar-modify-form-dialog\"\n    >\n      <el-form\n        ref=\"modifyFormRef\"\n        :model=\"modifyFormData\"\n        :rules=\"modifyFormRules\"\n        label-width=\"120px\"\n        v-loading=\"modifyFormSubmitting\"\n      >\n        <!-- 开始时间 -->\n        <el-form-item :label=\"$t('reservation.startTime')\" prop=\"startDateTime\">\n          <el-date-picker\n            v-model=\"modifyFormData.startDateTime\"\n            type=\"datetime\"\n            :placeholder=\"$t('reservation.selectStartTime')\"\n            style=\"width: 100%\"\n            :picker-options=\"dateTimePickerOptions\"\n            value-format=\"yyyy-MM-ddTHH:mm:ss\"\n            format=\"yyyy-MM-dd HH:mm:ss\"\n            @change=\"checkModifyTimeAvailability\"\n          ></el-date-picker>\n        </el-form-item>\n\n        <!-- 结束时间 -->\n        <el-form-item :label=\"$t('reservation.endTime')\" prop=\"endDateTime\">\n          <el-date-picker\n            v-model=\"modifyFormData.endDateTime\"\n            type=\"datetime\"\n            :placeholder=\"$t('reservation.selectEndTime')\"\n            style=\"width: 100%\"\n            :picker-options=\"dateTimePickerOptions\"\n            value-format=\"yyyy-MM-ddTHH:mm:ss\"\n            format=\"yyyy-MM-dd HH:mm:ss\"\n            @change=\"checkModifyTimeAvailability\"\n          ></el-date-picker>\n        </el-form-item>\n\n        <!-- 时间冲突提示 -->\n        <el-alert\n          v-if=\"modifyTimeConflict\"\n          :title=\"modifyTimeConflictTitle\"\n          type=\"error\"\n          :closable=\"false\"\n          show-icon\n          style=\"margin-bottom: 15px;\"\n        >\n          <div v-if=\"modifyConflictingReservations && modifyConflictingReservations.length > 0\">\n            <p style=\"margin-bottom: 10px;\">与以下预定时间冲突：</p>\n            <div v-for=\"conflict in modifyConflictingReservations\" :key=\"conflict.id\" style=\"margin-bottom: 8px; padding: 8px; background-color: #fef0f0; border-radius: 4px;\">\n              <div><strong>时间：</strong>{{ conflict.start_datetime }} 至 {{ conflict.end_datetime }}</div>\n              <div><strong>使用人：</strong>{{ conflict.user_name }} ({{ conflict.user_department }})</div>\n              <div v-if=\"conflict.user_email\"><strong>邮箱：</strong>{{ conflict.user_email }}</div>\n              <div v-if=\"conflict.user_phone\"><strong>电话：</strong>{{ conflict.user_phone }}</div>\n              <div v-if=\"conflict.purpose\"><strong>用途：</strong>{{ conflict.purpose }}</div>\n            </div>\n          </div>\n          <template v-else-if=\"modifyConflictMessage\">\n            {{ modifyConflictMessage }}\n          </template>\n        </el-alert>\n\n        <!-- 时间可用提示 -->\n        <el-alert\n          v-if=\"!modifyTimeConflict && modifyTimeAvailabilityChecked\"\n          title=\"该时间段可用\"\n          type=\"success\"\n          :closable=\"false\"\n          show-icon\n          style=\"margin-bottom: 15px;\"\n        ></el-alert>\n\n        <!-- 使用目的 -->\n        <el-form-item :label=\"$t('reservation.purpose')\" prop=\"purpose\">\n          <el-input\n            v-model=\"modifyFormData.purpose\"\n            :placeholder=\"$t('reservation.purposePlaceholder')\"\n            type=\"textarea\"\n            :rows=\"3\"\n          ></el-input>\n        </el-form-item>\n\n        <!-- 用户邮箱 -->\n        <el-form-item :label=\"$t('reservation.userEmail')\" prop=\"userEmail\">\n          <el-input\n            v-model=\"modifyFormData.userEmail\"\n            :placeholder=\"$t('reservation.emailPlaceholder')\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"modifyFormDialogVisible = false\">{{ $t('common.cancel') }}</el-button>\n        <el-button type=\"primary\" @click=\"submitModifyForm\" :loading=\"modifyFormSubmitting\" :disabled=\"modifyTimeConflict\">\n          {{ $t('common.confirm') }}\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\n// import { Calendar } from '@fullcalendar/core';\nimport dayGridPlugin from '@fullcalendar/daygrid';\nimport timeGridPlugin from '@fullcalendar/timegrid';\nimport interactionPlugin from '@fullcalendar/interaction';\nimport FullCalendar from '@fullcalendar/vue';\nimport { formatDate } from '@/utils/date';\nimport reservationApi from '@/api/reservation';\nimport equipmentApi from '@/api/equipment';\n\nexport default {\n  name: 'CalendarView',\n  components: {\n    FullCalendar\n  },\n  data() {\n    return {\n      calendarOptions: {\n        plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin],\n        initialView: 'dayGridMonth',\n        headerToolbar: false, // 我们使用自定义的头部控件\n        events: [], // 将通过API加载\n        eventClick: this.handleEventClick,\n        eventDidMount: this.handleEventDidMount,\n        datesSet: this.handleDatesSet,\n        locale: this.$i18n.locale === 'zh-CN' ? 'zh-cn' : 'en',\n        firstDay: 1, // 周一作为一周的第一天\n        allDaySlot: false, // 不显示\"全天\"选项\n        slotMinTime: '00:00:00', // 从0点开始\n        slotMaxTime: '24:00:00', // 到24点结束\n        height: 'auto',\n        nowIndicator: true, // 显示当前时间指示器\n        slotLabelFormat: {\n          hour: '2-digit',\n          minute: '2-digit',\n          hour12: false // 使用24小时制\n        },\n        slotEventOverlap: false, // 禁止事件重叠\n        eventTimeFormat: { // 统一时间格式\n          hour: '2-digit',\n          minute: '2-digit',\n          hour12: false\n        },\n        titleFormat: { year: 'numeric', month: 'long' }, // 标题格式\n        views: {\n          dayGridMonth: {\n            dayHeaderFormat: { weekday: 'long' }, // 月视图只显示星期几\n            fixedWeekCount: false, // 根据当月天数动态调整行数\n            showNonCurrentDates: false // 隐藏不属于当前月份的日期\n          },\n          timeGridWeek: {\n            dayHeaderFormat: { weekday: 'long', month: 'numeric', day: 'numeric', omitCommas: true } // 周视图显示完整日期\n          },\n          timeGridDay: {\n            dayHeaderFormat: { weekday: 'long', month: 'numeric', day: 'numeric', omitCommas: true }, // 日视图显示完整日期\n            slotEventOverlap: false, // 日视图特别禁止事件重叠\n            eventMaxStack: 4, // 最多显示4个事件，超过则显示\"+更多\"\n            moreLinkClick: 'popover' // 点击\"+更多\"时显示弹窗\n          }\n        },\n        locales: {\n          'zh-cn': {\n            week: {\n              dow: 1, // 周一作为一周的第一天\n              doy: 4  // 一年中第一周必须包含1月4日\n            },\n            buttonText: {\n              today: '今天',\n              month: '月',\n              week: '周',\n              day: '日'\n            },\n            weekText: '周',\n            allDayText: '全天',\n            moreLinkText: '更多',\n            noEventsText: '没有事件'\n          },\n          'en': {\n            week: {\n              dow: 1, // 周一作为一周的第一天\n              doy: 4  // 一年中第一周必须包含1月4日\n            },\n            buttonText: {\n              today: 'Today',\n              month: 'Month',\n              week: 'Week',\n              day: 'Day'\n            },\n            weekText: 'W',\n            allDayText: 'All day',\n            moreLinkText: 'more',\n            noEventsText: 'No events'\n          }\n        }\n      },\n      detailVisible: false,\n      selectedEvent: null,\n      loading: false,\n      currentViewTitle: '',\n      cancelDialogVisible: false,\n      cancelling: false,\n      cancelForm: {\n        reservationCode: ''\n      },\n      cancelRules: {\n        reservationCode: [\n          { required: true, message: this.$t('reservation.codeOrContactRequired'), trigger: 'blur' }\n        ]\n      },\n\n      // 修改预约相关\n      modifyDialogVisible: false,\n      modifying: false,\n      modifyForm: {\n        reservationCode: ''\n      },\n      modifyRules: {\n        reservationCode: [\n          { required: true, message: this.$t('reservation.codeOrContactRequired'), trigger: 'blur' }\n        ]\n      },\n\n      // 修改预约表单相关\n      modifyFormDialogVisible: false,\n      modifyFormSubmitting: false,\n      modifyTimeConflict: false,\n      modifyConflictMessage: '',\n      modifyConflictingReservations: [],\n      modifyTimeAvailabilityChecked: false,\n      modifyFormData: {\n        startDateTime: '',\n        endDateTime: '',\n        purpose: '',\n        userEmail: ''\n      },\n      modifyFormRules: {\n        startDateTime: [\n          { required: true, message: this.$t('reservation.startTimeRequired'), trigger: 'change' }\n        ],\n        endDateTime: [\n          { required: true, message: this.$t('reservation.endTimeRequired'), trigger: 'change' }\n        ],\n        userEmail: [\n          { required: true, message: this.$t('reservation.emailRequired'), trigger: 'blur' },\n          { type: 'email', message: this.$t('reservation.emailFormat'), trigger: 'blur' }\n        ]\n      },\n      dateTimePickerOptions: {\n        disabledDate: (time) => {\n          return time.getTime() < Date.now() - 8.64e7; // 8.64e7是一天的毫秒数\n        }\n      },\n\n      // 设备筛选相关\n      selectedEquipment: null,\n      equipmentList: []\n    };\n  },\n\n  computed: {\n    // 修改时间冲突标题\n    modifyTimeConflictTitle() {\n      if (this.modifyConflictingReservations && this.modifyConflictingReservations.length > 0) {\n        return `所选时间段与 ${this.modifyConflictingReservations.length} 个预定冲突`\n      } else if (this.modifyConflictMessage) {\n        return this.modifyConflictMessage\n      } else {\n        return '所选时间段已被预定'\n      }\n    }\n  },\n\n  mounted() {\n    this.loadEvents();\n    this.loadEquipmentList();\n  },\n\n  beforeDestroy() {\n    // 清除资源\n  },\n  watch: {\n    // 监听语言变化\n    '$i18n.locale': {\n      handler(newLocale) {\n        if (this.$refs.fullCalendar) {\n          const calendarApi = this.$refs.fullCalendar.getApi();\n          calendarApi.setOption('locale', newLocale === 'zh-CN' ? 'zh-cn' : 'en');\n          this.currentViewTitle = calendarApi.view.title;\n        }\n      },\n      immediate: true\n    }\n  },\n  methods: {\n    // 加载预约数据\n    async loadEvents() {\n      this.loading = true;\n      try {\n        const calendarApi = this.$refs.fullCalendar.getApi();\n        const start = this.formatDate(calendarApi.view.activeStart);\n        const end = this.formatDate(calendarApi.view.activeEnd);\n\n        // 构建请求参数\n        const params = {\n          start_date: start,\n          end_date: end\n        };\n\n        // 如果选择了设备，添加设备ID参数\n        if (this.selectedEquipment) {\n          params.equipment_id = this.selectedEquipment;\n        }\n\n        const response = await this.$http.get('/api/reservations/calendar', { params });\n\n        if (response.data.success) {\n          calendarApi.removeAllEvents();\n          calendarApi.addEventSource(response.data.events);\n        } else {\n          this.$message.error(response.data.message || this.$t('calendar.loadFailed'));\n        }\n      } catch (error) {\n        console.error('Failed to load calendar events:', error);\n        this.$message.error(this.$t('calendar.loadFailed'));\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    // 加载设备列表\n    async loadEquipmentList() {\n      try {\n        const response = await equipmentApi.getEquipments({ limit: 100 });\n        if (response.data && response.data.items) {\n          this.equipmentList = response.data.items;\n        }\n      } catch (error) {\n        console.error('Failed to load equipment list:', error);\n        this.$message.error(this.$t('error.serverError'));\n      }\n    },\n\n    // 处理设备选择变化\n    handleEquipmentChange() {\n      this.loadEvents();\n    },\n\n    // 处理日期范围变化\n    handleDatesSet() {\n      // 更新当前视图标题\n      const calendarApi = this.$refs.fullCalendar.getApi();\n      this.currentViewTitle = calendarApi.view.title;\n\n      // 加载事件数据\n      this.loadEvents();\n    },\n\n    // 处理事件点击\n    handleEventClick(info) {\n      this.selectedEvent = info.event;\n\n      // 确保 extendedProps 中包含 reservationCode 和 reservationNumber\n      if (info.event.extendedProps) {\n        // 如果没有 reservationCode，尝试从其他属性中获取\n        if (!info.event.extendedProps.reservationCode && info.event.extendedProps.reservation_code) {\n          info.event.extendedProps.reservationCode = info.event.extendedProps.reservation_code;\n        }\n\n        // 如果没有 reservationNumber，尝试从其他属性中获取\n        if (!info.event.extendedProps.reservationNumber && info.event.extendedProps.reservation_number) {\n          info.event.extendedProps.reservationNumber = info.event.extendedProps.reservation_number;\n        }\n      }\n\n      this.detailVisible = true;\n    },\n\n    // 处理事件渲染\n    handleEventDidMount(info) {\n      // 获取事件状态\n      const status = info.event.extendedProps.status;\n\n      // 根据状态设置事件颜色（适用于所有视图）\n      if (status) {\n        if (status === 'confirmed') {\n          info.el.style.backgroundColor = 'rgba(103, 194, 58, 0.7)'; // 已确认 - 半透明绿色\n          info.el.style.borderColor = 'rgba(103, 194, 58, 0.9)';\n        } else if (status === 'in_use') {\n          info.el.style.backgroundColor = 'rgba(64, 158, 255, 0.7)'; // 使用中 - 半透明蓝色\n          info.el.style.borderColor = 'rgba(64, 158, 255, 0.9)';\n        }\n      }\n\n      // 添加鼠标悬停工具提示\n      this.addEventTooltip(info);\n\n      // 为循环预约添加标记 - 适用于所有视图\n      if (info.event.extendedProps.isRecurring) {\n        // 月视图的处理方式\n        if (info.view.type === 'dayGridMonth') {\n          const recurringIcon = document.createElement('span');\n          recurringIcon.className = 'recurring-icon';\n          recurringIcon.innerHTML = '<i class=\"el-icon-refresh-right\"></i>';\n          const titleEl = info.el.querySelector('.fc-event-title');\n          if (titleEl) {\n            titleEl.appendChild(recurringIcon);\n          }\n        }\n        // 周视图和日视图的处理方式\n        else if (info.view.type === 'timeGridWeek' || info.view.type === 'timeGridDay') {\n          // 为事件添加循环图标标记\n          const recurringIcon = document.createElement('span');\n          recurringIcon.className = 'recurring-icon-timegrid';\n          recurringIcon.innerHTML = '<i class=\"el-icon-refresh-right\"></i>';\n\n          // 添加到自定义内容中\n          const eventContent = info.el.querySelector('.custom-event-content');\n          if (eventContent) {\n            eventContent.appendChild(recurringIcon);\n          } else {\n            // 如果还没有自定义内容，添加到事件主体\n            const mainContent = info.el.querySelector('.fc-event-main');\n            if (mainContent) {\n              mainContent.appendChild(recurringIcon);\n            }\n          }\n        }\n      }\n\n      // 在月视图中修改时间格式为24小时制\n      if (info.view.type === 'dayGridMonth') {\n        const timeEl = info.el.querySelector('.fc-event-time');\n        if (timeEl && info.event.start) {\n          // 使用24小时制格式化时间\n          const formattedTime = info.event.start.toLocaleTimeString('zh-CN', {\n            hour: '2-digit',\n            minute: '2-digit',\n            hour12: false\n          });\n          timeEl.textContent = formattedTime;\n        }\n      }\n\n      // 在周视图和日视图中自定义事件内容\n      if (info.view.type === 'timeGridWeek' || info.view.type === 'timeGridDay') {\n        // 计算事件持续时间（分钟）\n        const start = info.event.start;\n        const end = info.event.end;\n        const durationMs = end - start;\n        const durationMinutes = Math.round(durationMs / (1000 * 60));\n\n        // 清除默认内容\n        const eventContent = info.el.querySelector('.fc-event-main');\n        if (eventContent) {\n          // 创建自定义内容\n          const customContent = document.createElement('div');\n          customContent.className = 'custom-event-content';\n\n          // 根据预约时长决定显示内容\n          if (durationMinutes <= 30) {\n            // 短时间预约：只显示设备名称+时间（一排）\n            const shortContent = document.createElement('div');\n            shortContent.className = 'event-short-content';\n\n            // 提取设备名称（取第一个单词或前两个单词）\n            const titleParts = info.event.title.split(' ');\n            let deviceName = titleParts[0];\n            if (titleParts.length > 1 &&\n                (titleParts[1] === 'Mic' ||\n                 titleParts[1] === 'Speaker' ||\n                 titleParts[1].startsWith('Speaker'))) {\n              deviceName = `${titleParts[0]} ${titleParts[1]}`;\n            }\n\n            // 格式化时间\n            const startTime = start ? start.toLocaleTimeString('zh-CN', {hour: '2-digit', minute:'2-digit', hour12: false}) : '';\n            const endTime = end ? end.toLocaleTimeString('zh-CN', {hour: '2-digit', minute:'2-digit', hour12: false}) : '';\n\n            shortContent.textContent = `${deviceName} ${startTime}-${endTime}`;\n            shortContent.style.fontWeight = 'bold';\n            shortContent.style.fontSize = '0.9em';\n            shortContent.style.fontc\n            shortContent.style.textAlign = 'center';\n            shortContent.style.lineHeight = '1.2';\n\n            customContent.appendChild(shortContent);\n          } else {\n            // 长时间预约：保持原有的3排显示\n            // 设备名称\n            const title = document.createElement('div');\n            title.className = 'event-title';\n            title.textContent = info.event.title;\n            customContent.appendChild(title);\n\n            // 使用人\n            const user = document.createElement('div');\n            user.className = 'event-user';\n            user.textContent = info.event.extendedProps.userName || '';\n            customContent.appendChild(user);\n\n            // 时间\n            const time = document.createElement('div');\n            time.className = 'event-time';\n            const startTime = start ? start.toLocaleTimeString('zh-CN', {hour: '2-digit', minute:'2-digit', hour12: false}) : '';\n            const endTime = end ? end.toLocaleTimeString('zh-CN', {hour: '2-digit', minute:'2-digit', hour12: false}) : '';\n            time.textContent = `${startTime}-${endTime}`;\n            customContent.appendChild(time);\n          }\n\n          // 替换内容\n          eventContent.innerHTML = '';\n          eventContent.appendChild(customContent);\n\n          // 为循环预约添加图标\n          if (info.event.extendedProps.isRecurring) {\n            const recurringIcon = document.createElement('span');\n            recurringIcon.className = 'recurring-icon-timegrid';\n            recurringIcon.innerHTML = '<i class=\"el-icon-refresh-right\"></i>';\n            eventContent.appendChild(recurringIcon);\n          }\n        }\n      }\n    },\n\n    // 切换视图\n    changeView(viewName) {\n      const calendarApi = this.$refs.fullCalendar.getApi();\n      calendarApi.changeView(viewName);\n      // 更新当前视图类型\n      this.calendarOptions.initialView = viewName;\n    },\n\n    // 跳转到今天\n    today() {\n      const calendarApi = this.$refs.fullCalendar.getApi();\n      calendarApi.today();\n    },\n\n    // 上一个时间段\n    prev() {\n      const calendarApi = this.$refs.fullCalendar.getApi();\n      calendarApi.prev();\n    },\n\n    // 下一个时间段\n    next() {\n      const calendarApi = this.$refs.fullCalendar.getApi();\n      calendarApi.next();\n    },\n\n    // 获取状态标签类型\n    getStatusTagType(status) {\n      const statusMap = {\n        'confirmed': 'success',\n        'in_use': 'primary',\n        'cancelled': 'info',\n        'expired': 'danger'\n      };\n      return statusMap[status] || 'info';\n    },\n\n    // 格式化日期\n    formatDate(date) {\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\n    },\n\n    // 格式化日期时间\n    formatDateTime(date) {\n      // 不进行时区转换（第三个参数设为false），避免时间多加8小时\n      return formatDate(date, 'YYYY-MM-DD HH:mm', false);\n    },\n\n    // 格式化短时间（只显示小时和分钟）\n    formatShortTime(date) {\n      if (!date) return '';\n      return date.toLocaleTimeString('zh-CN', {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n      });\n    },\n\n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        'confirmed': this.$t('reservation.confirmed'),\n        'in_use': this.$t('reservation.inUse'),\n        'cancelled': this.$t('reservation.cancelled'),\n        'expired': this.$t('reservation.expired')\n      };\n      return statusMap[status] || status;\n    },\n\n    // 添加事件工具提示\n    addEventTooltip(info) {\n      // 获取事件信息\n      const event = info.event;\n      const props = event.extendedProps;\n\n      // 计算事件持续时间（分钟）\n      const start = event.start;\n      const end = event.end;\n      const durationMs = end - start;\n      const durationMinutes = Math.round(durationMs / (1000 * 60));\n\n      // 获取当前视图类型\n      const calendarApi = this.$refs.fullCalendar.getApi();\n      const viewType = calendarApi.view.type;\n\n      // 获取事件元素\n      const titleEl = info.el.querySelector('.fc-event-title');\n      const timeEl = info.el.querySelector('.fc-event-time');\n      const mainEl = info.el.querySelector('.fc-event-main');\n\n      // 根据视图类型处理事件显示\n      if (viewType === 'dayGridMonth') {\n        // 月视图：所有预约显示相同，显示设备名称+开始和结束时间\n        if (titleEl) {\n          // 提取完整设备名称（通常是前两个单词，如\"M2F Mic\"）\n          const titleParts = event.title.split(' ');\n          let deviceName = titleParts[0]; // 至少包含第一个单词\n\n          // 如果第二个单词是\"Mic\"或其他设备类型标识，也包含它\n          if (titleParts.length > 1 &&\n              (titleParts[1] === 'Mic' ||\n               titleParts[1] === 'Speaker' ||\n               titleParts[1].startsWith('Speaker'))) {\n            deviceName = `${titleParts[0]} ${titleParts[1]}`;\n          }\n\n          // 格式化时间\n          const startTime = this.formatShortTime(start);\n          const endTime = this.formatShortTime(end);\n\n          // 设置标题为\"设备名称 开始-结束\"\n          titleEl.textContent = `${deviceName}     ${startTime}-${endTime}`;\n\n          // 完全移除时间元素，防止重复显示时间\n          if (timeEl) {\n            timeEl.remove(); // 彻底移除时间元素，而不仅仅是隐藏\n          }\n        }\n      } else if (viewType === 'timeGridWeek' || viewType === 'timeGridDay') {\n        // 周视图和日视图：短时间预约特殊处理\n        if (durationMinutes <= 30) {\n          // 添加短时间预约的类\n          info.el.classList.add('very-short-duration-event');\n\n          // 只显示设备名称\n          if (titleEl) {\n            const deviceName = event.title.split(' ')[0];\n            titleEl.textContent = deviceName;\n          }\n\n          // 调整主容器样式\n          if (mainEl) {\n            mainEl.style.display = 'flex';\n            mainEl.style.alignItems = 'center';\n            mainEl.style.justifyContent = 'center';\n            mainEl.style.padding = '2px';\n\n            // 日视图和周视图的不同布局\n            if (viewType === 'timeGridDay') {\n              // 日视图：水平排列\n              mainEl.style.flexDirection = 'row';\n              if (timeEl) {\n                timeEl.style.marginRight = '4px';\n              }\n            } else {\n              // 周视图：垂直排列\n              mainEl.style.flexDirection = 'column';\n              if (timeEl) {\n                timeEl.style.marginBottom = '2px';\n              }\n            }\n          }\n\n          // 调整字体样式\n          if (titleEl) {\n            titleEl.style.fontWeight = 'bold';\n            titleEl.style.fontSize = '0.9em';\n          }\n          if (timeEl) {\n            timeEl.style.fontWeight = 'bold';\n            timeEl.style.fontSize = '0.9em';\n          }\n        }\n      }\n\n      // 设置title属性（原生浏览器工具提示）\n      const tooltipText = `${event.title}\\n${props.userName || ''}\\n${this.formatDateTime(start)} - ${this.formatDateTime(end)}\\n${this.getStatusText(props.status)}\\n${props.reservationNumber}`;\n      info.el.setAttribute('title', tooltipText);\n    },\n\n    // 显示取消预约对话框\n    showCancelDialog() {\n      this.cancelForm.reservationCode = '';\n      this.cancelDialogVisible = true;\n    },\n\n    // 检查预约是否已开始\n    isReservationStarted(event) {\n      const now = new Date();\n      const startTime = new Date(event.start);\n      return startTime <= now;\n    },\n\n    // 显示修改对话框\n    showModifyDialog() {\n      this.modifyForm.reservationCode = '';\n      this.modifyDialogVisible = true;\n    },\n\n    // 确认修改预约\n    async confirmModifyDialog() {\n      try {\n        // 验证表单\n        await this.$refs.modifyForm.validate();\n\n        // 检查输入的预约码是否与当前预约码匹配\n        if (this.modifyForm.reservationCode !== this.selectedEvent.extendedProps.reservationCode) {\n          this.$message.error(this.$t('reservation.checkCodeAndContact'));\n          return;\n        }\n\n        // 关闭验证对话框\n        this.modifyDialogVisible = false;\n\n        // 初始化修改表单数据\n        let startDateTime, endDateTime;\n\n        try {\n          startDateTime = new Date(this.selectedEvent.start);\n          endDateTime = new Date(this.selectedEvent.end);\n\n          // 检查日期是否有效\n          if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime())) {\n            throw new Error('Invalid date');\n          }\n        } catch (e) {\n          console.error('Error creating date objects:', e);\n          // 使用当前时间作为默认值\n          startDateTime = new Date();\n          endDateTime = new Date();\n          endDateTime.setHours(endDateTime.getHours() + 1); // 结束时间默认为当前时间加1小时\n        }\n\n        // 手动格式化日期时间，确保格式与el-date-picker的value-format属性匹配\n        const formatDateTimeForPicker = (date) => {\n          if (!date || isNaN(date.getTime())) return null;\n\n          const year = date.getFullYear();\n          const month = String(date.getMonth() + 1).padStart(2, '0');\n          const day = String(date.getDate()).padStart(2, '0');\n          const hours = String(date.getHours()).padStart(2, '0');\n          const minutes = String(date.getMinutes()).padStart(2, '0');\n          const seconds = String(date.getSeconds()).padStart(2, '0');\n\n          return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;\n        };\n\n        this.modifyFormData = {\n          startDateTime: formatDateTimeForPicker(startDateTime),\n          endDateTime: formatDateTimeForPicker(endDateTime),\n          purpose: this.selectedEvent.extendedProps.purpose || '',\n          userEmail: this.selectedEvent.extendedProps.userEmail || ''\n        };\n\n        // 显示修改表单对话框\n        this.modifyFormDialogVisible = true;\n      } catch (error) {\n        console.error('Failed to validate modification form:', error);\n        this.$message.error(this.$t('error.serverError'));\n      } finally {\n        this.modifying = false;\n      }\n    },\n\n    // 验证时间范围\n    validateTimeRange() {\n      const startTime = new Date(this.modifyFormData.startDateTime);\n      const endTime = new Date(this.modifyFormData.endDateTime);\n\n      if (startTime >= endTime) {\n        this.$message.error(this.$t('reservation.invalidTime'));\n        return false;\n      }\n\n      return true;\n    },\n\n    // 检查修改时间可用性\n    async checkModifyTimeAvailability() {\n      if (!this.modifyFormData.startDateTime || !this.modifyFormData.endDateTime) {\n        this.modifyTimeAvailabilityChecked = false\n        return\n      }\n\n      // 添加更严格的验证\n      if (this.modifyFormData.startDateTime >= this.modifyFormData.endDateTime) {\n        this.modifyTimeConflict = true\n        this.modifyTimeAvailabilityChecked = false\n        return\n      }\n\n      try {\n        const equipmentId = this.selectedEvent.extendedProps.equipmentId\n        const startDate = this.modifyFormData.startDateTime\n        const endDate = this.modifyFormData.endDateTime\n\n        // 调用API检查时间可用性，排除当前预定\n        const excludeId = this.selectedEvent.id  // 使用事件的id字段，而不是extendedProps.reservationId\n        const params = {\n          start_date: startDate,\n          end_date: endDate\n        }\n\n        // 只有当excludeId存在且不为null/undefined时才添加参数\n        if (excludeId != null && excludeId !== undefined) {\n          params.exclude_reservation_id = excludeId\n        }\n\n\n\n        const response = await this.$http.get(`/api/equipment/${equipmentId}/availability`, { params })\n\n        // 检查是否有冲突 - 处理API响应格式\n        if (response.data.specific_time_check) {\n          // 如果是具体时间段检查\n          console.log('具体时间段检查结果:', response.data.available)\n          this.modifyTimeConflict = response.data.available.includes(false)\n        } else {\n          // 如果是按日期检查\n          console.log('按日期检查结果:', response.data.available)\n          this.modifyTimeConflict = response.data.available.includes(false)\n        }\n\n        // 设置冲突信息\n        if (this.modifyTimeConflict) {\n          console.log('检测到时间冲突:', response.data.available)\n\n          // 获取冲突的预定信息\n          this.modifyConflictingReservations = response.data.conflicting_reservations || []\n\n          // 检查是否是因为达到最大同时预定数量\n          if (response.data.allow_simultaneous && response.data.max_simultaneous > 1) {\n            this.modifyConflictMessage = `该时间段已达到最大同时预定数量(${response.data.max_simultaneous})`;\n          } else {\n            this.modifyConflictMessage = '';\n          }\n        } else {\n          console.log('时间段可用')\n          this.modifyConflictMessage = '';\n          this.modifyConflictingReservations = [];\n        }\n\n        this.modifyTimeAvailabilityChecked = true\n      } catch (error) {\n        console.error('Failed to check availability:', error)\n        this.modifyTimeConflict = true\n        this.modifyTimeAvailabilityChecked = false\n        this.modifyConflictingReservations = []\n        this.$message.error(this.$t('common.error'))\n      }\n    },\n\n    // 提交修改表单\n    async submitModifyForm() {\n      try {\n        // 验证表单\n        await this.$refs.modifyFormRef.validate();\n\n        // 验证时间范围\n        if (!this.validateTimeRange()) return;\n\n        // 检查时间冲突\n        if (this.modifyTimeConflict) {\n          this.$message.error(this.$t('reservation.timeConflict'))\n          return\n        }\n\n        this.modifyFormSubmitting = true;\n\n        // 构建更新数据\n        const updateData = {\n          start_datetime: this.modifyFormData.startDateTime,\n          end_datetime: this.modifyFormData.endDateTime,\n          purpose: this.modifyFormData.purpose || undefined,\n          user_email: this.modifyFormData.userEmail || undefined,\n          lang: this.$i18n.locale\n        };\n\n        // 调用更新API - 传递预约序号以确保修改正确的子预约\n        const response = await reservationApi.updateReservation(\n          this.selectedEvent.extendedProps.reservationCode,\n          updateData,\n          this.selectedEvent.extendedProps.reservationNumber  // 传递预约序号\n        );\n\n        if (response.data && response.data.success) {\n          this.$message.success(this.$t('reservation.updateSuccess'));\n          this.modifyFormDialogVisible = false;\n\n          // 关闭预约详情弹窗\n          this.detailVisible = false;\n\n          // 重新加载日历事件\n          this.loadEvents();\n        } else {\n          const errorMessage = response.data && response.data.message ? response.data.message : this.$t('reservation.updateFailed');\n          this.$message.error(errorMessage);\n        }\n      } catch (error) {\n        console.error('Failed to update reservation:', error);\n        this.$message.error(this.$t('error.serverError'));\n      } finally {\n        this.modifyFormSubmitting = false;\n      }\n    },\n\n    // 取消预约\n    async cancelReservation() {\n      try {\n        // 验证表单\n        await this.$refs.cancelForm.validate();\n\n        // 检查输入的预约码是否与当前预约码匹配\n        if (this.cancelForm.reservationCode !== this.selectedEvent.extendedProps.reservationCode) {\n          this.$message.error(this.$t('reservation.checkCodeAndContact'));\n          return;\n        }\n\n        this.cancelling = true;\n\n        // 准备请求数据\n        const data = {\n          reservation_number: this.selectedEvent.extendedProps.reservationNumber || null,\n          lang: this.$i18n.locale\n        };\n\n        // 调用取消预约API\n        const response = await reservationApi.cancelReservation(this.selectedEvent.extendedProps.reservationCode, data);\n\n        if (response.data.success) {\n          this.$message.success(this.$t('reservation.cancelSuccess'));\n          this.cancelDialogVisible = false;\n\n          // 关闭预约详情弹窗\n          this.detailVisible = false;\n\n          // 重新加载日历事件\n          this.loadEvents();\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.cancelFailed'));\n        }\n      } catch (error) {\n        console.error('Failed to cancel reservation:', error);\n        this.$message.error(this.$t('error.serverError'));\n      } finally {\n        this.cancelling = false;\n      }\n    }\n  }\n};\n</script>\n\n<style scoped>\n.calendar-view {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n.calendar-header {\n  margin-bottom: 20px;\n}\n\n.calendar-current-date {\n  font-size: 1.2rem;\n  color: #606266;\n  margin-top: 5px;\n  font-weight: normal;\n}\n\n.calendar-controls {\n  display: flex;\n  justify-content: flex-end;\n  gap: 10px;\n}\n\n.filter-row {\n  margin-top: 15px;\n}\n\n.equipment-filter {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 15px;\n}\n\n.status-legend-row {\n  margin-top: 10px;\n  margin-bottom: 15px;\n}\n\n.status-legend {\n  width: 100%;\n}\n\n.status-legend-content {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.status-colors {\n  display: flex;\n  flex-wrap: wrap;\n  align-items: center;\n  gap: 15px;\n}\n\n.status-item {\n  display: flex;\n  align-items: center;\n  font-size: 14px;\n}\n\n.status-color {\n  display: inline-block;\n  width: 16px;\n  height: 16px;\n  border-radius: 3px;\n  margin-right: 5px;\n}\n\n.confirmed-color {\n  background-color: rgba(103, 194, 58, 0.7);\n  border: 1px solid rgba(103, 194, 58, 0.9);\n}\n\n.inuse-color {\n  background-color: rgba(64, 158, 255, 0.7);\n  border: 1px solid rgba(64, 158, 255, 0.9);\n}\n\n.cancel-tip-container {\n  margin-top: 5px;\n}\n\n.cancel-tip {\n  font-weight: bold;\n}\n\n.cancel-tip i {\n  margin-right: 5px;\n  color: #ff4040;\n}\n\n\n.update-tip-container {\n  margin-top: 5px;\n}\n\n.update-tip {\n  font-weight: bold;\n}\n\n.update-tip i {\n  margin-right: 5px;\n  color: #40a9ff;\n}\n\n/* 移动端适配 */\n@media screen and (max-width: 768px) {\n  .calendar-view {\n    padding: 10px;\n  }\n\n  .calendar-header {\n    margin-bottom: 15px;\n  }\n\n  /* 日历头部布局调整 */\n  .calendar-header .el-row {\n    flex-direction: column;\n    gap: 15px;\n  }\n\n  .calendar-header .el-col {\n    width: 100% !important;\n    max-width: 100% !important;\n    flex: none !important;\n  }\n\n  /* 标题区域 */\n  .calendar-header .el-col:first-child {\n    text-align: center;\n  }\n\n  .calendar-header h1 {\n    font-size: 1.5rem;\n    margin-bottom: 5px;\n  }\n\n  .calendar-current-date {\n    font-size: 1rem;\n    margin-top: 0;\n  }\n\n  /* 控制按钮区域 */\n  .calendar-controls {\n    justify-content: center;\n    flex-wrap: wrap;\n    gap: 8px;\n  }\n\n  .calendar-controls .el-button-group {\n    margin-bottom: 8px;\n  }\n\n  .calendar-controls .el-button {\n    font-size: 12px;\n    padding: 6px 12px;\n  }\n\n  /* 设备筛选器 */\n  .equipment-filter .el-select {\n    width: 100% !important;\n    max-width: 300px;\n  }\n\n  /* 状态图例 */\n  .status-colors {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n\n  .status-legend-content {\n    gap: 8px;\n  }\n\n  .status-item {\n    font-size: 13px;\n  }\n\n  /* FullCalendar 移动端优化 */\n  /* 日历头部星期几显示优化 */\n  :deep(.fc-col-header-cell) {\n    padding: 4px 2px !important;\n    font-size: 12px !important;\n  }\n\n  :deep(.fc-col-header-cell-cushion) {\n    padding: 2px !important;\n    font-size: 12px !important;\n    line-height: 1.2 !important;\n    word-break: break-word !important;\n    white-space: normal !important;\n    text-align: center !important;\n  }\n\n  /* 月视图中的星期几文字换行 */\n  :deep(.fc-daygrid-header .fc-col-header-cell-cushion) {\n    white-space: normal !important;\n    word-wrap: break-word !important;\n    overflow-wrap: break-word !important;\n    hyphens: auto !important;\n    max-width: 100% !important;\n    display: block !important;\n    height: auto !important;\n    min-height: 30px !important;\n    display: flex !important;\n    align-items: center !important;\n    justify-content: center !important;\n  }\n\n  /* 周视图和日视图的头部优化 */\n  :deep(.fc-timegrid-header .fc-col-header-cell-cushion) {\n    font-size: 11px !important;\n    line-height: 1.1 !important;\n    padding: 2px 1px !important;\n    white-space: normal !important;\n    word-break: break-word !important;\n  }\n\n  /* 日期数字样式 */\n  :deep(.fc-daygrid-day-number) {\n    font-size: 14px !important;\n    padding: 2px !important;\n  }\n\n  /* 时间轴标签 */\n  :deep(.fc-timegrid-slot-label) {\n    font-size: 11px !important;\n    padding: 2px !important;\n  }\n\n  /* 事件文字大小调整 */\n  :deep(.fc-event-title) {\n    font-size: 11px !important;\n    line-height: 1.2 !important;\n  }\n\n  :deep(.fc-event-time) {\n    font-size: 10px !important;\n  }\n\n  /* 月视图事件优化 */\n  :deep(.fc-daygrid-event) {\n    font-size: 11px !important;\n    padding: 1px 2px !important;\n    margin: 1px 0 !important;\n  }\n\n  /* 时间网格事件优化 */\n  :deep(.fc-timegrid-event) {\n    font-size: 10px !important;\n    min-height: 20px !important;\n  }\n\n  /* 今天按钮和导航按钮 */\n  :deep(.fc-button) {\n    font-size: 12px !important;\n    padding: 4px 8px !important;\n  }\n\n  /* 特别针对英文星期几名称的换行处理 */\n  :deep(.fc-col-header-cell-cushion) {\n    /* 强制英文单词换行 */\n    word-break: break-all !important;\n    overflow-wrap: anywhere !important;\n    /* 设置最小高度确保有足够空间显示换行文字 */\n    min-height: 35px !important;\n    /* 使用flex布局居中显示 */\n    display: flex !important;\n    align-items: center !important;\n    justify-content: center !important;\n    text-align: center !important;\n    /* 允许多行显示 */\n    white-space: normal !important;\n    line-height: 1.1 !important;\n  }\n\n  /* 日历表格头部行高调整 */\n  :deep(.fc-col-header) {\n    height: auto !important;\n    min-height: 40px !important;\n  }\n\n  :deep(.fc-col-header-row) {\n    height: auto !important;\n    min-height: 40px !important;\n  }\n\n  /* 确保日历头部有足够的空间 */\n  :deep(.fc-daygrid-header) {\n    margin-bottom: 5px !important;\n  }\n\n  /* 月视图日期格子高度调整 */\n  :deep(.fc-daygrid-day) {\n    min-height: 80px !important;\n  }\n\n  /* 周视图和日视图的列头部高度 */\n  :deep(.fc-timegrid-header .fc-col-header-cell) {\n    height: auto !important;\n    min-height: 40px !important;\n  }\n\n  /* 移动端周末字体颜色强制设置为红色 */\n  /* 周末表头文字颜色 - 红色 */\n  :deep(.fc-col-header-cell.fc-day-sat),\n  :deep(.fc-col-header-cell.fc-day-sun) {\n    color: #ff0000 !important;\n  }\n\n  :deep(.fc-col-header-cell.fc-day-sat .fc-col-header-cell-cushion),\n  :deep(.fc-col-header-cell.fc-day-sun .fc-col-header-cell-cushion) {\n    color: #ff0000 !important;\n  }\n\n  /* 月视图中周末日期数字颜色 - 红色 */\n  :deep(.fc-daygrid-day.fc-day-sat .fc-daygrid-day-number),\n  :deep(.fc-daygrid-day.fc-day-sun .fc-daygrid-day-number) {\n    color: #ff0000 !important;\n  }\n\n  /* 周视图和日视图中周末列头文字样式 - 红色 */\n  :deep(.fc-timeGridWeek-view .fc-col-header-cell.fc-day-sat .fc-col-header-cell-cushion),\n  :deep(.fc-timeGridWeek-view .fc-col-header-cell.fc-day-sun .fc-col-header-cell-cushion),\n  :deep(.fc-timeGridDay-view .fc-col-header-cell.fc-day-sat .fc-col-header-cell-cushion),\n  :deep(.fc-timeGridDay-view .fc-col-header-cell.fc-day-sun .fc-col-header-cell-cushion) {\n    color: #ff0000 !important;\n  }\n\n  /* 确保周末的所有文字都是红色，包括换行后的文字 */\n  :deep(.fc-day-sat .fc-col-header-cell-cushion),\n  :deep(.fc-day-sun .fc-col-header-cell-cushion),\n  :deep(.fc-day-sat),\n  :deep(.fc-day-sun) {\n    color: #ff0000 !important;\n  }\n}\n\n.recurring-notice {\n  margin: 15px 0;\n}\n\n.action-buttons {\n  margin-top: 15px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n.cancel-content {\n  padding: 10px 0;\n}\n\n.recurring-icon {\n  margin-left: 5px;\n  color: #ff9800;\n}\n\n/* 周视图和日视图中的循环预约图标 */\n:deep(.recurring-icon-timegrid) {\n  position: absolute;\n  top: 2px;\n  right: 2px;\n  color: rgba(255, 255, 255, 0.9);\n  font-size: 12px;\n  z-index: 2;\n  background-color: rgba(0, 0, 0, 0.3);\n  border-radius: 50%;\n  width: 16px;\n  height: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  pointer-events: none; /* 确保图标不会影响点击事件 */\n}\n\n/* 自定义事件样式 */\n:deep(.fc-event) {\n  cursor: pointer;\n}\n\n:deep(.fc-event-title) {\n  font-weight: bold;\n}\n\n/* 隐藏月视图中的点 */\n:deep(.fc-daygrid-event-dot) {\n  display: none !important;\n}\n\n/* 月视图中的事件内容居中显示 */\n:deep(.fc-daygrid-event) {\n  display: flex !important;\n  flex-direction: row !important;\n  align-items: center !important;\n  justify-content: center !important;\n  text-align: center !important;\n  padding: 2px 4px !important;\n  white-space: nowrap !important;\n}\n\n:deep(.fc-daygrid-event-harness) {\n  margin-top: 2px !important;\n}\n\n:deep(.fc-daygrid-event .fc-event-main) {\n  width: 100% !important;\n  text-align: center !important;\n  display: flex !important;\n  justify-content: center !important;\n  align-items: center !important;\n}\n\n:deep(.fc-daygrid-event .fc-event-time) {\n  font-weight: bold !important;\n  margin-right: 4px !important;\n  text-align: center !important;\n  display: inline !important;\n}\n\n:deep(.fc-daygrid-event .fc-event-title) {\n  display: inline !important;\n  text-align: center !important;\n}\n\n/* 日期头部样式 */\n:deep(.fc-col-header-cell) {\n  background-color: #f5f7fa;\n  padding: 8px 0;\n}\n\n:deep(.fc-col-header-cell-cushion) {\n  font-weight: bold;\n  color: #303133;\n  text-decoration: none;\n  padding: 4px;\n}\n\n/* 时间格子样式 */\n:deep(.fc-timegrid-slot) {\n  height: 40px;\n}\n\n/* 设置事件的最小高度 */\n:deep(.fc-timegrid-event) {\n  min-height: 40px !important; /* 确保短时间预约有足够的显示空间 */\n}\n\n/* 今天高亮 - 使用突出边框而非背景色 */\n:deep(.fc-day-today) {\n  background-color: transparent !important; /* 移除背景色 */\n  position: relative !important;\n}\n\n/* 为月视图中的今天添加突出边框 */\n:deep(.fc-daygrid-day.fc-day-today::after) {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border: 1px solid #006eff !important; /* 红色细线边框 */\n  pointer-events: none; /* 确保边框不会影响点击事件 */\n  z-index: 1; /* 确保边框在内容之上 */\n}\n\n/* 月视图中今天的日期数字加粗显示 */\n:deep(.fc-day-today .fc-daygrid-day-number) {\n  font-weight: bold !important;\n  color: #006eff !important; /* 红色文字 */\n}\n\n/* 周视图和日视图中今天的列头样式 */\n:deep(.fc-timeGridWeek-view .fc-col-header-cell.fc-day-today),\n:deep(.fc-timeGridDay-view .fc-col-header-cell.fc-day-today) {\n  background-color: transparent !important;\n}\n\n/* 周视图和日视图中今天的列头文字样式 */\n:deep(.fc-timeGridWeek-view .fc-col-header-cell.fc-day-today .fc-col-header-cell-cushion),\n:deep(.fc-timeGridDay-view .fc-col-header-cell.fc-day-today .fc-col-header-cell-cushion) {\n  font-weight: bold !important;\n  color: #006eff !important; /* 红色文字 */\n}\n\n/* 周视图中今天的时间轴列样式 */\n:deep(.fc-timeGridWeek-view .fc-timegrid-col.fc-day-today) {\n  background-color: transparent !important;\n  position: relative !important;\n}\n\n/* 为周视图中今天的时间轴添加红色细线边框 */\n:deep(.fc-timeGridWeek-view .fc-timegrid-col.fc-day-today::after) {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border-top: 1px solid #006eff !important;\n  border-left: 1px solid #006eff !important;\n  border-right: 1px solid #006eff !important;\n  border-bottom: 1px solid #006eff !important;\n  pointer-events: none;\n  z-index: 1;\n}\n\n/* 日视图中今天的时间轴列样式 */\n:deep(.fc-timeGridDay-view .fc-timegrid-col.fc-day-today) {\n  background-color: transparent !important;\n  /* 日视图不需要边框 */\n}\n\n/* 周末颜色 */\n:deep(.fc-day-sat), :deep(.fc-day-sun) {\n  background-color: #ffffff;\n}\n\n/* 周末表头文字颜色 - 红色 */\n:deep(.fc-col-header-cell.fc-day-sat), :deep(.fc-col-header-cell.fc-day-sun) {\n  color: #ff0000;\n}\n\n:deep(.fc-col-header-cell.fc-day-sat .fc-col-header-cell-cushion),\n:deep(.fc-col-header-cell.fc-day-sun .fc-col-header-cell-cushion) {\n  color: #ff0000;\n}\n\n/* 月视图中周末日期数字颜色 - 红色 */\n:deep(.fc-daygrid-day.fc-day-sat .fc-daygrid-day-number),\n:deep(.fc-daygrid-day.fc-day-sun .fc-daygrid-day-number) {\n  color: #ff0000;\n}\n\n/* 工作日颜色 */\n:deep(.fc-day-mon), :deep(.fc-day-tue), :deep(.fc-day-wed), :deep(.fc-day-thu), :deep(.fc-day-fri) {\n  background-color: #ffffff;\n}\n\n/* 预约详情弹窗样式 */\n.event-detail-card {\n  border-radius: 4px;\n  overflow: hidden;\n}\n\n.event-header {\n  padding: 15px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.event-header h3 {\n  margin: 0;\n  font-size: 18px;\n}\n\n.status-confirmed {\n  background-color: #f0f9eb;\n  border-left: 4px solid #67c23a;\n}\n\n.status-in_use {\n  background-color: #ecf5ff;\n  border-left: 4px solid #409eff;\n}\n\n.status-cancelled {\n  background-color: #f4f4f5;\n  border-left: 4px solid #909399;\n}\n\n.status-expired {\n  background-color: #fef0f0;\n  border-left: 4px solid #f56c6c;\n}\n\n.event-info {\n  padding: 0 15px 15px;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.info-item i {\n  margin-right: 10px;\n  color: #909399;\n}\n\n.time-info {\n  margin-bottom: 15px;\n}\n\n.time-display {\n  font-size: 16px;\n  font-weight: bold;\n  color: #303133;\n}\n\n/* 周视图和日视图中的事件内容样式 */\n:deep(.custom-event-content) {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  padding: 2px;\n  text-align: center;\n  overflow: hidden;\n}\n\n/* 当事件宽度较窄时的样式调整 */\n:deep(.fc-timegrid-col-events .custom-event-content) {\n  font-size: 0.9em;\n  line-height: 1.1;\n}\n\n/* 修复日视图和周视图中事件宽度问题 - 允许并排显示 */\n:deep(.fc-timegrid-event-harness) {\n  /* 移除强制宽度，允许事件并排显示 */\n  border-radius: 3px !important;\n}\n\n:deep(.fc-timegrid-event) {\n  border-radius: 3px !important;\n  margin: 1px !important;\n  padding: 2px !important;\n}\n\n/* 确保事件内容在较窄的事件中也能正常显示 */\n:deep(.fc-timegrid-event .fc-event-main) {\n  padding: 2px !important;\n}\n\n:deep(.fc-timeGridDay-view .fc-timegrid-event) {\n  /* 移除强制宽度，允许事件并排显示 */\n  width: auto !important;\n  max-width: none !important;\n  margin: 1px 2px !important;\n  border-radius: 3px !important;\n  box-sizing: border-box !important;\n}\n\n/* 修复日视图中的列宽度 */\n:deep(.fc-timeGridDay-view .fc-timegrid-col) {\n  width: 100% !important;\n  max-width: 100% !important;\n}\n\n:deep(.fc-timeGridDay-view .fc-timegrid-col-frame) {\n  width: 100% !important;\n  max-width: 100% !important;\n}\n\n/* 日视图中的事件容器 */\n:deep(.fc-timeGridDay-view .fc-timegrid-col-events) {\n  display: flex !important;\n  flex-direction: column !important;\n}\n\n/* 日视图中的事件容器组 */\n:deep(.fc-timeGridDay-view .fc-timegrid-event-harness-inset) {\n  margin-left: 0 !important;\n  margin-right: 0 !important;\n  width: auto !important;\n  flex: 1 !important;\n}\n\n/* 日视图中的事件 */\n:deep(.fc-timeGridDay-view .fc-timegrid-event) {\n  flex: 1 !important;\n  min-width: 0 !important;\n}\n\n/* 日视图中的事件内容 */\n:deep(.fc-timeGridDay-view .custom-event-content) {\n  padding: 2px !important;\n  font-size: 0.9em !important;\n  line-height: 1.1 !important;\n}\n\n/* 日视图中的事件容器 */\n:deep(.fc-timeGridDay-view .fc-timegrid-event-harness) {\n  margin: 0 1px !important;\n}\n\n/* 日视图中的事件 */\n:deep(.fc-timeGridDay-view .fc-timegrid-event) {\n  margin: 1px !important;\n  border-radius: 3px !important;\n}\n\n/* 日视图中的更多链接 */\n:deep(.fc-timeGridDay-view .fc-daygrid-more-link) {\n  font-size: 12px !important;\n  font-weight: bold !important;\n  color: #409eff !important;\n  background-color: rgba(64, 158, 255, 0.1) !important;\n  border-radius: 3px !important;\n  padding: 2px 4px !important;\n  margin: 1px !important;\n  text-align: center !important;\n}\n\n/* 日视图中的弹出窗口 */\n:deep(.fc-timeGridDay-view .fc-popover) {\n  border-radius: 4px !important;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;\n  border: 1px solid #ebeef5 !important;\n}\n\n/* 短时间预约的特殊样式（30分钟或更短） */\n:deep(.very-short-duration-event) {\n  min-height: 30px !important; /* 确保最小高度 */\n  font-weight: bold !important; /* 加粗文字 */\n  border-width: 2px !important; /* 加粗边框 */\n  z-index: 6 !important; /* 更高层级 */\n  overflow: visible !important; /* 允许内容溢出 */\n  height: auto !important; /* 自动调整高度 */\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important; /* 添加阴影，增加立体感 */\n}\n\n/* 短时间预约的标题样式 */\n:deep(.very-short-duration-event .fc-event-title) {\n  font-size: 1em !important; /* 保持正常字体大小 */\n  line-height: 1.2 !important; /* 减小行高 */\n  padding: 2px !important; /* 减小内边距 */\n  text-align: center !important; /* 文字居中 */\n  white-space: nowrap !important; /* 不允许文字换行 */\n  overflow: hidden !important; /* 隐藏溢出内容 */\n  text-overflow: ellipsis !important; /* 显示省略号 */\n  font-weight: bold !important; /* 加粗文字 */\n  color: #000 !important; /* 黑色文字，增加可读性 */\n}\n\n/* 确保时间显示清晰 */\n:deep(.very-short-duration-event .fc-event-time) {\n  font-size: 1em !important; /* 保持正常字体大小 */\n  font-weight: bold !important;\n  padding: 2px !important;\n  text-align: center !important;\n  color: #000 !important; /* 黑色文字，增加可读性 */\n}\n\n/* 日视图中的短时间预约 */\n:deep(.fc-timeGridDay-view .very-short-duration-event) {\n  min-height: 30px !important; /* 固定最小高度 */\n  margin-top: 2px !important;\n  margin-bottom: 2px !important;\n  height: 30px !important; /* 固定高度 */\n  padding: 2px !important; /* 增加内边距 */\n  border-width: 2px !important; /* 加粗边框 */\n}\n\n/* 日视图中的短时间预约内容 */\n:deep(.fc-timeGridDay-view .very-short-duration-event .fc-event-main) {\n  padding: 2px !important; /* 增加内边距 */\n  display: flex !important;\n  flex-direction: row !important; /* 水平排列 */\n  justify-content: center !important;\n  align-items: center !important;\n  height: 100% !important;\n}\n\n/* 日视图中的短时间预约时间和标题 */\n:deep(.fc-timeGridDay-view .very-short-duration-event .fc-event-time),\n:deep(.fc-timeGridDay-view .very-short-duration-event .fc-event-title) {\n  display: inline-block !important;\n  padding: 0 3px !important; /* 增加内边距 */\n  margin: 0 !important;\n  line-height: 1.2 !important; /* 增加行高 */\n  font-size: 1em !important; /* 增加字体大小 */\n  font-weight: bold !important; /* 加粗文字 */\n  text-shadow: 0px 0px 1px rgba(0, 0, 0, 0.5) !important; /* 添加文字阴影，增加可读性 */\n}\n\n/* 周视图中的短时间预约 */\n:deep(.fc-timeGridWeek-view .very-short-duration-event) {\n  min-height: 30px !important;\n  margin-top: 1px !important;\n  margin-bottom: 1px !important;\n  height: 30px !important; /* 固定高度 */\n}\n\n/* 周视图中的短时间预约内容 */\n:deep(.fc-timeGridWeek-view .very-short-duration-event .fc-event-main) {\n  padding: 1px !important;\n  display: flex !important;\n  flex-direction: row !important; /* 水平排列 */\n  justify-content: center !important;\n  align-items: center !important;\n  height: 100% !important;\n}\n\n/* 周视图中的短时间预约时间和标题 */\n:deep(.fc-timeGridWeek-view .very-short-duration-event .fc-event-time),\n:deep(.fc-timeGridWeek-view .very-short-duration-event .fc-event-title) {\n  display: inline-block !important;\n  padding: 0 2px !important;\n  margin: 0 !important;\n  line-height: 1.2 !important;\n  font-size: 1em !important;\n  font-weight: bold !important;\n  color: #000 !important; /* 黑色文字，增加可读性 */\n}\n\n/* 月视图中的事件样式 */\n:deep(.fc-daygrid-event) {\n  padding: 2px 4px !important;\n  margin: 1px 0 !important;\n  border-radius: 3px !important;\n}\n\n:deep(.event-title) {\n  font-weight: bold;\n  margin-bottom: 2px;\n  font-size: 12px;\n  line-height: 1.1;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 100%;\n  color: #000; /* 黑色标题 */\n}\n\n:deep(.event-user) {\n  font-size: 11px;\n  color: #333; /* 深灰色用户名 */\n  margin-bottom: 2px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 100%;\n}\n\n:deep(.event-time) {\n  font-size: 11px;\n  font-weight: bold;\n  color: #333; /* 深灰色时间 */\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 100%;\n}\n\n/* 当事件宽度足够大时的样式 */\n:deep(.fc-timegrid-event-harness-inset .event-title) {\n  font-size: 14px;\n  margin-bottom: 4px;\n  color: #000; /* 黑色标题 */\n}\n\n:deep(.fc-timegrid-event-harness-inset .event-user) {\n  font-size: 13px;\n  margin-bottom: 4px;\n  color: #333; /* 深灰色用户名 */\n}\n\n:deep(.fc-timegrid-event-harness-inset .event-time) {\n  font-size: 13px;\n  color: #333; /* 深灰色时间 */\n}\n\n/* 当前时间指示器样式 */\n:deep(.fc-timegrid-now-indicator-line) {\n  border-color: #ff0000; /* 红色线条 */\n  border-width: 0.1px; /* 加粗线条 */\n  z-index: 10; /* 确保在事件上方显示 */\n}\n\n:deep(.fc-timegrid-now-indicator-arrow) {\n  border-color: #ff0000; /* 红色箭头 */\n  border-width: 5px 0px 0 0; /* 加大箭头 */\n}\n\n/* 修复日视图右侧的多余空白 */\n:deep(.fc-timegrid-slots table),\n:deep(.fc-timegrid-cols table) {\n  width: 100% !important;\n}\n\n/* 让日视图列容器更好地利用可用空间 */\n:deep(.fc-timegrid-col.fc-day) {\n  padding-right: 0 !important; /* 移除右侧内边距 */\n  max-width: none !important; /* 移除最大宽度限制 */\n}\n\n/* 修复日视图整体容器宽度 */\n:deep(.fc-timegrid-body) {\n  width: 100% !important;\n}\n\n/* 移除日视图主体右侧的内边距/外边距 */\n:deep(.fc-timegrid-body .fc-scroller-liquid-absolute) {\n  right: 0 !important;\n}\n\n/* 预约块布局优化 */\n:deep(.fc-timegrid-event-harness) {\n  /* 保持原有的自适应宽度，但移除过多的边距 */\n  margin: 1px !important;\n}\n\n/* 确保同时间段的预约能合理并排 */\n:deep(.fc-timegrid-col-events) {\n  /* 仅调整右边距，不影响自适应布局 */\n  margin-right: 0 !important;\n  right: 0 !important;\n}\n\n/* 短时间预约样式 */\n:deep(.event-short-content) {\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  height: 100% !important;\n  padding: 2px 4px !important;\n  white-space: nowrap !important;\n  overflow: hidden !important;\n  text-overflow: ellipsis !important;\n}\n\n/* 确保短时间预约的容器样式 */\n:deep(.custom-event-content .event-short-content) {\n  width: 100% !important;\n  min-height: 20px !important;\n}\n\n/* 移动端弹窗优化 */\n@media (max-width: 768px) {\n  /* 预约详情弹窗移动端优化 */\n  :deep(.calendar-detail-dialog .el-dialog) {\n    width: 85% !important;\n    margin: 0 auto !important;\n    top: 5vh !important;\n  }\n\n  :deep(.calendar-detail-dialog .el-dialog__body) {\n    padding: 15px !important;\n    max-height: 70vh;\n    overflow-y: auto;\n  }\n\n  /* 取消预约弹窗移动端优化 */\n  :deep(.calendar-cancel-dialog .el-dialog) {\n    width: 80% !important;\n    margin: 0 auto !important;\n    top: 10vh !important;\n  }\n\n  :deep(.calendar-cancel-dialog .el-dialog__body) {\n    padding: 15px !important;\n  }\n\n  /* 修改预约弹窗移动端优化 */\n  :deep(.calendar-modify-dialog .el-dialog) {\n    width: 80% !important;\n    margin: 0 auto !important;\n    top: 10vh !important;\n  }\n\n  :deep(.calendar-modify-dialog .el-dialog__body) {\n    padding: 15px !important;\n  }\n\n  /* 修改预约表单弹窗移动端优化 */\n  :deep(.calendar-modify-form-dialog .el-dialog) {\n    width: 95% !important;\n    margin: 0 auto !important;\n    top: 5vh !important;\n  }\n\n  :deep(.calendar-modify-form-dialog .el-dialog__body) {\n    padding: 15px !important;\n    max-height: 70vh;\n    overflow-y: auto;\n  }\n\n  /* 移动端遮罩层优化 - 确保遮罩层不会阻挡弹窗交互 */\n  :deep(.v-modal) {\n    background-color: rgba(0, 0, 0, 0.3) !important; /* 降低遮罩层透明度 */\n    pointer-events: none !important; /* 让遮罩层不阻挡点击事件 */\n  }\n\n  /* 确保弹窗本身可以接收点击事件 */\n  :deep(.el-dialog__wrapper) {\n    pointer-events: auto !important;\n  }\n\n  :deep(.el-dialog) {\n    pointer-events: auto !important;\n  }\n\n  /* 弹窗按钮在移动端的优化 */\n  :deep(.action-buttons) {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  :deep(.action-buttons .el-button) {\n    width: 100% !important;\n    margin: 0 !important;\n  }\n\n  :deep(.dialog-footer .el-button) {\n    width: 48% !important;\n    margin: 0 1% !important;\n  }\n}\n\n</style>\n\n"], "mappings": "AA2UA;AACA,OAAAA,aAAA;AACA,OAAAC,cAAA;AACA,OAAAC,iBAAA;AACA,OAAAC,YAAA;AACA,SAAAC,UAAA;AACA,OAAAC,cAAA;AACA,OAAAC,YAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL;EACA;EACAM,KAAA;IACA;MACAC,eAAA;QACAC,OAAA,GAAAX,aAAA,EAAAC,cAAA,EAAAC,iBAAA;QACAU,WAAA;QACAC,aAAA;QAAA;QACAC,MAAA;QAAA;QACAC,UAAA,OAAAC,gBAAA;QACAC,aAAA,OAAAC,mBAAA;QACAC,QAAA,OAAAC,cAAA;QACAC,MAAA,OAAAC,KAAA,CAAAD,MAAA;QACAE,QAAA;QAAA;QACAC,UAAA;QAAA;QACAC,WAAA;QAAA;QACAC,WAAA;QAAA;QACAC,MAAA;QACAC,YAAA;QAAA;QACAC,eAAA;UACAC,IAAA;UACAC,MAAA;UACAC,MAAA;QACA;QACAC,gBAAA;QAAA;QACAC,eAAA;UAAA;UACAJ,IAAA;UACAC,MAAA;UACAC,MAAA;QACA;QACAG,WAAA;UAAAC,IAAA;UAAAC,KAAA;QAAA;QAAA;QACAC,KAAA;UACAC,YAAA;YACAC,eAAA;cAAAC,OAAA;YAAA;YAAA;YACAC,cAAA;YAAA;YACAC,mBAAA;UACA;UACAC,YAAA;YACAJ,eAAA;cAAAC,OAAA;cAAAJ,KAAA;cAAAQ,GAAA;cAAAC,UAAA;YAAA;UACA;UACAC,WAAA;YACAP,eAAA;cAAAC,OAAA;cAAAJ,KAAA;cAAAQ,GAAA;cAAAC,UAAA;YAAA;YAAA;YACAb,gBAAA;YAAA;YACAe,aAAA;YAAA;YACAC,aAAA;UACA;QACA;QACAC,OAAA;UACA;YACAC,IAAA;cACAC,GAAA;cAAA;cACAC,GAAA;YACA;YACAC,UAAA;cACAC,KAAA;cACAlB,KAAA;cACAc,IAAA;cACAN,GAAA;YACA;YACAW,QAAA;YACAC,UAAA;YACAC,YAAA;YACAC,YAAA;UACA;UACA;YACAR,IAAA;cACAC,GAAA;cAAA;cACAC,GAAA;YACA;YACAC,UAAA;cACAC,KAAA;cACAlB,KAAA;cACAc,IAAA;cACAN,GAAA;YACA;YACAW,QAAA;YACAC,UAAA;YACAC,YAAA;YACAC,YAAA;UACA;QACA;MACA;MACAC,aAAA;MACAC,aAAA;MACAC,OAAA;MACAC,gBAAA;MACAC,mBAAA;MACAC,UAAA;MACAC,UAAA;QACAC,eAAA;MACA;MACAC,WAAA;QACAD,eAAA,GACA;UAAAE,QAAA;UAAAC,OAAA,OAAAC,EAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAC,mBAAA;MACAC,SAAA;MACAC,UAAA;QACAR,eAAA;MACA;MACAS,WAAA;QACAT,eAAA,GACA;UAAAE,QAAA;UAAAC,OAAA,OAAAC,EAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAK,uBAAA;MACAC,oBAAA;MACAC,kBAAA;MACAC,qBAAA;MACAC,6BAAA;MACAC,6BAAA;MACAC,cAAA;QACAC,aAAA;QACAC,WAAA;QACAC,OAAA;QACAC,SAAA;MACA;MACAC,eAAA;QACAJ,aAAA,GACA;UAAAf,QAAA;UAAAC,OAAA,OAAAC,EAAA;UAAAC,OAAA;QAAA,EACA;QACAa,WAAA,GACA;UAAAhB,QAAA;UAAAC,OAAA,OAAAC,EAAA;UAAAC,OAAA;QAAA,EACA;QACAe,SAAA,GACA;UAAAlB,QAAA;UAAAC,OAAA,OAAAC,EAAA;UAAAC,OAAA;QAAA,GACA;UAAAiB,IAAA;UAAAnB,OAAA,OAAAC,EAAA;UAAAC,OAAA;QAAA;MAEA;MACAkB,qBAAA;QACAC,YAAA,EAAAC,IAAA;UACA,OAAAA,IAAA,CAAAC,OAAA,KAAAC,IAAA,CAAAC,GAAA;QACA;MACA;MAEA;MACAC,iBAAA;MACAC,aAAA;IACA;EACA;EAEAC,QAAA;IACA;IACAC,wBAAA;MACA,SAAAlB,6BAAA,SAAAA,6BAAA,CAAAmB,MAAA;QACA,sBAAAnB,6BAAA,CAAAmB,MAAA;MACA,gBAAApB,qBAAA;QACA,YAAAA,qBAAA;MACA;QACA;MACA;IACA;EACA;EAEAqB,QAAA;IACA,KAAAC,UAAA;IACA,KAAAC,iBAAA;EACA;EAEAC,cAAA;IACA;EAAA,CACA;EACAC,KAAA;IACA;IACA;MACAC,QAAAC,SAAA;QACA,SAAAC,KAAA,CAAAC,YAAA;UACA,MAAAC,WAAA,QAAAF,KAAA,CAAAC,YAAA,CAAAE,MAAA;UACAD,WAAA,CAAAE,SAAA,WAAAL,SAAA;UACA,KAAA5C,gBAAA,GAAA+C,WAAA,CAAAG,IAAA,CAAAC,KAAA;QACA;MACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;IACA;IACA,MAAAd,WAAA;MACA,KAAAxC,OAAA;MACA;QACA,MAAAgD,WAAA,QAAAF,KAAA,CAAAC,YAAA,CAAAE,MAAA;QACA,MAAAM,KAAA,QAAAjH,UAAA,CAAA0G,WAAA,CAAAG,IAAA,CAAAK,WAAA;QACA,MAAAC,GAAA,QAAAnH,UAAA,CAAA0G,WAAA,CAAAG,IAAA,CAAAO,SAAA;;QAEA;QACA,MAAAC,MAAA;UACAC,UAAA,EAAAL,KAAA;UACAM,QAAA,EAAAJ;QACA;;QAEA;QACA,SAAAvB,iBAAA;UACAyB,MAAA,CAAAG,YAAA,QAAA5B,iBAAA;QACA;QAEA,MAAA6B,QAAA,cAAAC,KAAA,CAAAC,GAAA;UAAAN;QAAA;QAEA,IAAAI,QAAA,CAAApH,IAAA,CAAAuH,OAAA;UACAlB,WAAA,CAAAmB,eAAA;UACAnB,WAAA,CAAAoB,cAAA,CAAAL,QAAA,CAAApH,IAAA,CAAAK,MAAA;QACA;UACA,KAAAqH,QAAA,CAAAC,KAAA,CAAAP,QAAA,CAAApH,IAAA,CAAA6D,OAAA,SAAAC,EAAA;QACA;MACA,SAAA6D,KAAA;QACAC,OAAA,CAAAD,KAAA,oCAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA,MAAA7D,EAAA;MACA;QACA,KAAAT,OAAA;MACA;IACA;IAEA;IACA,MAAAyC,kBAAA;MACA;QACA,MAAAsB,QAAA,SAAAvH,YAAA,CAAAgI,aAAA;UAAAC,KAAA;QAAA;QACA,IAAAV,QAAA,CAAApH,IAAA,IAAAoH,QAAA,CAAApH,IAAA,CAAA+H,KAAA;UACA,KAAAvC,aAAA,GAAA4B,QAAA,CAAApH,IAAA,CAAA+H,KAAA;QACA;MACA,SAAAJ,KAAA;QACAC,OAAA,CAAAD,KAAA,mCAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA,MAAA7D,EAAA;MACA;IACA;IAEA;IACAkE,sBAAA;MACA,KAAAnC,UAAA;IACA;IAEA;IACAlF,eAAA;MACA;MACA,MAAA0F,WAAA,QAAAF,KAAA,CAAAC,YAAA,CAAAE,MAAA;MACA,KAAAhD,gBAAA,GAAA+C,WAAA,CAAAG,IAAA,CAAAC,KAAA;;MAEA;MACA,KAAAZ,UAAA;IACA;IAEA;IACAtF,iBAAA0H,IAAA;MACA,KAAA7E,aAAA,GAAA6E,IAAA,CAAAC,KAAA;;MAEA;MACA,IAAAD,IAAA,CAAAC,KAAA,CAAAC,aAAA;QACA;QACA,KAAAF,IAAA,CAAAC,KAAA,CAAAC,aAAA,CAAAzE,eAAA,IAAAuE,IAAA,CAAAC,KAAA,CAAAC,aAAA,CAAAC,gBAAA;UACAH,IAAA,CAAAC,KAAA,CAAAC,aAAA,CAAAzE,eAAA,GAAAuE,IAAA,CAAAC,KAAA,CAAAC,aAAA,CAAAC,gBAAA;QACA;;QAEA;QACA,KAAAH,IAAA,CAAAC,KAAA,CAAAC,aAAA,CAAAE,iBAAA,IAAAJ,IAAA,CAAAC,KAAA,CAAAC,aAAA,CAAAG,kBAAA;UACAL,IAAA,CAAAC,KAAA,CAAAC,aAAA,CAAAE,iBAAA,GAAAJ,IAAA,CAAAC,KAAA,CAAAC,aAAA,CAAAG,kBAAA;QACA;MACA;MAEA,KAAAnF,aAAA;IACA;IAEA;IACA1C,oBAAAwH,IAAA;MACA;MACA,MAAAM,MAAA,GAAAN,IAAA,CAAAC,KAAA,CAAAC,aAAA,CAAAI,MAAA;;MAEA;MACA,IAAAA,MAAA;QACA,IAAAA,MAAA;UACAN,IAAA,CAAAO,EAAA,CAAAC,KAAA,CAAAC,eAAA;UACAT,IAAA,CAAAO,EAAA,CAAAC,KAAA,CAAAE,WAAA;QACA,WAAAJ,MAAA;UACAN,IAAA,CAAAO,EAAA,CAAAC,KAAA,CAAAC,eAAA;UACAT,IAAA,CAAAO,EAAA,CAAAC,KAAA,CAAAE,WAAA;QACA;MACA;;MAEA;MACA,KAAAC,eAAA,CAAAX,IAAA;;MAEA;MACA,IAAAA,IAAA,CAAAC,KAAA,CAAAC,aAAA,CAAAU,WAAA;QACA;QACA,IAAAZ,IAAA,CAAAzB,IAAA,CAAAxB,IAAA;UACA,MAAA8D,aAAA,GAAAC,QAAA,CAAAC,aAAA;UACAF,aAAA,CAAAG,SAAA;UACAH,aAAA,CAAAI,SAAA;UACA,MAAAC,OAAA,GAAAlB,IAAA,CAAAO,EAAA,CAAAY,aAAA;UACA,IAAAD,OAAA;YACAA,OAAA,CAAAE,WAAA,CAAAP,aAAA;UACA;QACA;QACA;QAAA,KACA,IAAAb,IAAA,CAAAzB,IAAA,CAAAxB,IAAA,uBAAAiD,IAAA,CAAAzB,IAAA,CAAAxB,IAAA;UACA;UACA,MAAA8D,aAAA,GAAAC,QAAA,CAAAC,aAAA;UACAF,aAAA,CAAAG,SAAA;UACAH,aAAA,CAAAI,SAAA;;UAEA;UACA,MAAAI,YAAA,GAAArB,IAAA,CAAAO,EAAA,CAAAY,aAAA;UACA,IAAAE,YAAA;YACAA,YAAA,CAAAD,WAAA,CAAAP,aAAA;UACA;YACA;YACA,MAAAS,WAAA,GAAAtB,IAAA,CAAAO,EAAA,CAAAY,aAAA;YACA,IAAAG,WAAA;cACAA,WAAA,CAAAF,WAAA,CAAAP,aAAA;YACA;UACA;QACA;MACA;;MAEA;MACA,IAAAb,IAAA,CAAAzB,IAAA,CAAAxB,IAAA;QACA,MAAAwE,MAAA,GAAAvB,IAAA,CAAAO,EAAA,CAAAY,aAAA;QACA,IAAAI,MAAA,IAAAvB,IAAA,CAAAC,KAAA,CAAAtB,KAAA;UACA;UACA,MAAA6C,aAAA,GAAAxB,IAAA,CAAAC,KAAA,CAAAtB,KAAA,CAAA8C,kBAAA;YACArI,IAAA;YACAC,MAAA;YACAC,MAAA;UACA;UACAiI,MAAA,CAAAG,WAAA,GAAAF,aAAA;QACA;MACA;;MAEA;MACA,IAAAxB,IAAA,CAAAzB,IAAA,CAAAxB,IAAA,uBAAAiD,IAAA,CAAAzB,IAAA,CAAAxB,IAAA;QACA;QACA,MAAA4B,KAAA,GAAAqB,IAAA,CAAAC,KAAA,CAAAtB,KAAA;QACA,MAAAE,GAAA,GAAAmB,IAAA,CAAAC,KAAA,CAAApB,GAAA;QACA,MAAA8C,UAAA,GAAA9C,GAAA,GAAAF,KAAA;QACA,MAAAiD,eAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,UAAA;;QAEA;QACA,MAAAN,YAAA,GAAArB,IAAA,CAAAO,EAAA,CAAAY,aAAA;QACA,IAAAE,YAAA;UACA;UACA,MAAAU,aAAA,GAAAjB,QAAA,CAAAC,aAAA;UACAgB,aAAA,CAAAf,SAAA;;UAEA;UACA,IAAAY,eAAA;YACA;YACA,MAAAI,YAAA,GAAAlB,QAAA,CAAAC,aAAA;YACAiB,YAAA,CAAAhB,SAAA;;YAEA;YACA,MAAAiB,UAAA,GAAAjC,IAAA,CAAAC,KAAA,CAAAzB,KAAA,CAAA0D,KAAA;YACA,IAAAC,UAAA,GAAAF,UAAA;YACA,IAAAA,UAAA,CAAAvE,MAAA,SACAuE,UAAA,iBACAA,UAAA,qBACAA,UAAA,IAAAG,UAAA;cACAD,UAAA,MAAAF,UAAA,OAAAA,UAAA;YACA;;YAEA;YACA,MAAAI,SAAA,GAAA1D,KAAA,GAAAA,KAAA,CAAA8C,kBAAA;cAAArI,IAAA;cAAAC,MAAA;cAAAC,MAAA;YAAA;YACA,MAAAgJ,OAAA,GAAAzD,GAAA,GAAAA,GAAA,CAAA4C,kBAAA;cAAArI,IAAA;cAAAC,MAAA;cAAAC,MAAA;YAAA;YAEA0I,YAAA,CAAAN,WAAA,MAAAS,UAAA,IAAAE,SAAA,IAAAC,OAAA;YACAN,YAAA,CAAAxB,KAAA,CAAA+B,UAAA;YACAP,YAAA,CAAAxB,KAAA,CAAAgC,QAAA;YACAR,YAAA,CAAAxB,KAAA,CAAAiC,KAAA;YACAT,YAAA,CAAAxB,KAAA,CAAAkC,SAAA;YACAV,YAAA,CAAAxB,KAAA,CAAAmC,UAAA;YAEAZ,aAAA,CAAAX,WAAA,CAAAY,YAAA;UACA;YACA;YACA;YACA,MAAAxD,KAAA,GAAAsC,QAAA,CAAAC,aAAA;YACAvC,KAAA,CAAAwC,SAAA;YACAxC,KAAA,CAAAkD,WAAA,GAAA1B,IAAA,CAAAC,KAAA,CAAAzB,KAAA;YACAuD,aAAA,CAAAX,WAAA,CAAA5C,KAAA;;YAEA;YACA,MAAAoE,IAAA,GAAA9B,QAAA,CAAAC,aAAA;YACA6B,IAAA,CAAA5B,SAAA;YACA4B,IAAA,CAAAlB,WAAA,GAAA1B,IAAA,CAAAC,KAAA,CAAAC,aAAA,CAAA2C,QAAA;YACAd,aAAA,CAAAX,WAAA,CAAAwB,IAAA;;YAEA;YACA,MAAA1F,IAAA,GAAA4D,QAAA,CAAAC,aAAA;YACA7D,IAAA,CAAA8D,SAAA;YACA,MAAAqB,SAAA,GAAA1D,KAAA,GAAAA,KAAA,CAAA8C,kBAAA;cAAArI,IAAA;cAAAC,MAAA;cAAAC,MAAA;YAAA;YACA,MAAAgJ,OAAA,GAAAzD,GAAA,GAAAA,GAAA,CAAA4C,kBAAA;cAAArI,IAAA;cAAAC,MAAA;cAAAC,MAAA;YAAA;YACA4D,IAAA,CAAAwE,WAAA,MAAAW,SAAA,IAAAC,OAAA;YACAP,aAAA,CAAAX,WAAA,CAAAlE,IAAA;UACA;;UAEA;UACAmE,YAAA,CAAAJ,SAAA;UACAI,YAAA,CAAAD,WAAA,CAAAW,aAAA;;UAEA;UACA,IAAA/B,IAAA,CAAAC,KAAA,CAAAC,aAAA,CAAAU,WAAA;YACA,MAAAC,aAAA,GAAAC,QAAA,CAAAC,aAAA;YACAF,aAAA,CAAAG,SAAA;YACAH,aAAA,CAAAI,SAAA;YACAI,YAAA,CAAAD,WAAA,CAAAP,aAAA;UACA;QACA;MACA;IACA;IAEA;IACAiC,WAAAC,QAAA;MACA,MAAA3E,WAAA,QAAAF,KAAA,CAAAC,YAAA,CAAAE,MAAA;MACAD,WAAA,CAAA0E,UAAA,CAAAC,QAAA;MACA;MACA,KAAA/K,eAAA,CAAAE,WAAA,GAAA6K,QAAA;IACA;IAEA;IACAlI,MAAA;MACA,MAAAuD,WAAA,QAAAF,KAAA,CAAAC,YAAA,CAAAE,MAAA;MACAD,WAAA,CAAAvD,KAAA;IACA;IAEA;IACAmI,KAAA;MACA,MAAA5E,WAAA,QAAAF,KAAA,CAAAC,YAAA,CAAAE,MAAA;MACAD,WAAA,CAAA4E,IAAA;IACA;IAEA;IACAC,KAAA;MACA,MAAA7E,WAAA,QAAAF,KAAA,CAAAC,YAAA,CAAAE,MAAA;MACAD,WAAA,CAAA6E,IAAA;IACA;IAEA;IACAC,iBAAA5C,MAAA;MACA,MAAA6C,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAA7C,MAAA;IACA;IAEA;IACA5I,WAAA0L,IAAA;MACA,UAAAA,IAAA,CAAAC,WAAA,MAAAC,MAAA,CAAAF,IAAA,CAAAG,QAAA,QAAAC,QAAA,YAAAF,MAAA,CAAAF,IAAA,CAAAK,OAAA,IAAAD,QAAA;IACA;IAEA;IACAE,eAAAN,IAAA;MACA;MACA,OAAA1L,UAAA,CAAA0L,IAAA;IACA;IAEA;IACAO,gBAAAP,IAAA;MACA,KAAAA,IAAA;MACA,OAAAA,IAAA,CAAA3B,kBAAA;QACArI,IAAA;QACAC,MAAA;QACAC,MAAA;MACA;IACA;IAEA;IACAsK,cAAAtD,MAAA;MACA,MAAA6C,SAAA;QACA,kBAAAtH,EAAA;QACA,eAAAA,EAAA;QACA,kBAAAA,EAAA;QACA,gBAAAA,EAAA;MACA;MACA,OAAAsH,SAAA,CAAA7C,MAAA,KAAAA,MAAA;IACA;IAEA;IACAK,gBAAAX,IAAA;MACA;MACA,MAAAC,KAAA,GAAAD,IAAA,CAAAC,KAAA;MACA,MAAA4D,KAAA,GAAA5D,KAAA,CAAAC,aAAA;;MAEA;MACA,MAAAvB,KAAA,GAAAsB,KAAA,CAAAtB,KAAA;MACA,MAAAE,GAAA,GAAAoB,KAAA,CAAApB,GAAA;MACA,MAAA8C,UAAA,GAAA9C,GAAA,GAAAF,KAAA;MACA,MAAAiD,eAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,UAAA;;MAEA;MACA,MAAAvD,WAAA,QAAAF,KAAA,CAAAC,YAAA,CAAAE,MAAA;MACA,MAAAyF,QAAA,GAAA1F,WAAA,CAAAG,IAAA,CAAAxB,IAAA;;MAEA;MACA,MAAAmE,OAAA,GAAAlB,IAAA,CAAAO,EAAA,CAAAY,aAAA;MACA,MAAAI,MAAA,GAAAvB,IAAA,CAAAO,EAAA,CAAAY,aAAA;MACA,MAAA4C,MAAA,GAAA/D,IAAA,CAAAO,EAAA,CAAAY,aAAA;;MAEA;MACA,IAAA2C,QAAA;QACA;QACA,IAAA5C,OAAA;UACA;UACA,MAAAe,UAAA,GAAAhC,KAAA,CAAAzB,KAAA,CAAA0D,KAAA;UACA,IAAAC,UAAA,GAAAF,UAAA;;UAEA;UACA,IAAAA,UAAA,CAAAvE,MAAA,SACAuE,UAAA,iBACAA,UAAA,qBACAA,UAAA,IAAAG,UAAA;YACAD,UAAA,MAAAF,UAAA,OAAAA,UAAA;UACA;;UAEA;UACA,MAAAI,SAAA,QAAAsB,eAAA,CAAAhF,KAAA;UACA,MAAA2D,OAAA,QAAAqB,eAAA,CAAA9E,GAAA;;UAEA;UACAqC,OAAA,CAAAQ,WAAA,MAAAS,UAAA,QAAAE,SAAA,IAAAC,OAAA;;UAEA;UACA,IAAAf,MAAA;YACAA,MAAA,CAAAyC,MAAA;UACA;QACA;MACA,WAAAF,QAAA,uBAAAA,QAAA;QACA;QACA,IAAAlC,eAAA;UACA;UACA5B,IAAA,CAAAO,EAAA,CAAA0D,SAAA,CAAAC,GAAA;;UAEA;UACA,IAAAhD,OAAA;YACA,MAAAiB,UAAA,GAAAlC,KAAA,CAAAzB,KAAA,CAAA0D,KAAA;YACAhB,OAAA,CAAAQ,WAAA,GAAAS,UAAA;UACA;;UAEA;UACA,IAAA4B,MAAA;YACAA,MAAA,CAAAvD,KAAA,CAAA2D,OAAA;YACAJ,MAAA,CAAAvD,KAAA,CAAA4D,UAAA;YACAL,MAAA,CAAAvD,KAAA,CAAA6D,cAAA;YACAN,MAAA,CAAAvD,KAAA,CAAA8D,OAAA;;YAEA;YACA,IAAAR,QAAA;cACA;cACAC,MAAA,CAAAvD,KAAA,CAAA+D,aAAA;cACA,IAAAhD,MAAA;gBACAA,MAAA,CAAAf,KAAA,CAAAgE,WAAA;cACA;YACA;cACA;cACAT,MAAA,CAAAvD,KAAA,CAAA+D,aAAA;cACA,IAAAhD,MAAA;gBACAA,MAAA,CAAAf,KAAA,CAAAiE,YAAA;cACA;YACA;UACA;;UAEA;UACA,IAAAvD,OAAA;YACAA,OAAA,CAAAV,KAAA,CAAA+B,UAAA;YACArB,OAAA,CAAAV,KAAA,CAAAgC,QAAA;UACA;UACA,IAAAjB,MAAA;YACAA,MAAA,CAAAf,KAAA,CAAA+B,UAAA;YACAhB,MAAA,CAAAf,KAAA,CAAAgC,QAAA;UACA;QACA;MACA;;MAEA;MACA,MAAAkC,WAAA,MAAAzE,KAAA,CAAAzB,KAAA,KAAAqF,KAAA,CAAAhB,QAAA,gBAAAa,cAAA,CAAA/E,KAAA,YAAA+E,cAAA,CAAA7E,GAAA,WAAA+E,aAAA,CAAAC,KAAA,CAAAvD,MAAA,MAAAuD,KAAA,CAAAzD,iBAAA;MACAJ,IAAA,CAAAO,EAAA,CAAAoE,YAAA,UAAAD,WAAA;IACA;IAEA;IACAE,iBAAA;MACA,KAAApJ,UAAA,CAAAC,eAAA;MACA,KAAAH,mBAAA;IACA;IAEA;IACAuJ,qBAAA5E,KAAA;MACA,MAAA5C,GAAA,OAAAD,IAAA;MACA,MAAAiF,SAAA,OAAAjF,IAAA,CAAA6C,KAAA,CAAAtB,KAAA;MACA,OAAA0D,SAAA,IAAAhF,GAAA;IACA;IAEA;IACAyH,iBAAA;MACA,KAAA7I,UAAA,CAAAR,eAAA;MACA,KAAAM,mBAAA;IACA;IAEA;IACA,MAAAgJ,oBAAA;MACA;QACA;QACA,WAAA7G,KAAA,CAAAjC,UAAA,CAAA+I,QAAA;;QAEA;QACA,SAAA/I,UAAA,CAAAR,eAAA,UAAAN,aAAA,CAAA+E,aAAA,CAAAzE,eAAA;UACA,KAAAgE,QAAA,CAAAC,KAAA,MAAA7D,EAAA;UACA;QACA;;QAEA;QACA,KAAAE,mBAAA;;QAEA;QACA,IAAAW,aAAA,EAAAC,WAAA;QAEA;UACAD,aAAA,OAAAU,IAAA,MAAAjC,aAAA,CAAAwD,KAAA;UACAhC,WAAA,OAAAS,IAAA,MAAAjC,aAAA,CAAA0D,GAAA;;UAEA;UACA,IAAAoG,KAAA,CAAAvI,aAAA,CAAAS,OAAA,OAAA8H,KAAA,CAAAtI,WAAA,CAAAQ,OAAA;YACA,UAAA+H,KAAA;UACA;QACA,SAAAC,CAAA;UACAxF,OAAA,CAAAD,KAAA,iCAAAyF,CAAA;UACA;UACAzI,aAAA,OAAAU,IAAA;UACAT,WAAA,OAAAS,IAAA;UACAT,WAAA,CAAAyI,QAAA,CAAAzI,WAAA,CAAA0I,QAAA;QACA;;QAEA;QACA,MAAAC,uBAAA,GAAAlC,IAAA;UACA,KAAAA,IAAA,IAAA6B,KAAA,CAAA7B,IAAA,CAAAjG,OAAA;UAEA,MAAAzD,IAAA,GAAA0J,IAAA,CAAAC,WAAA;UACA,MAAA1J,KAAA,GAAA2J,MAAA,CAAAF,IAAA,CAAAG,QAAA,QAAAC,QAAA;UACA,MAAArJ,GAAA,GAAAmJ,MAAA,CAAAF,IAAA,CAAAK,OAAA,IAAAD,QAAA;UACA,MAAA+B,KAAA,GAAAjC,MAAA,CAAAF,IAAA,CAAAiC,QAAA,IAAA7B,QAAA;UACA,MAAAgC,OAAA,GAAAlC,MAAA,CAAAF,IAAA,CAAAqC,UAAA,IAAAjC,QAAA;UACA,MAAAkC,OAAA,GAAApC,MAAA,CAAAF,IAAA,CAAAuC,UAAA,IAAAnC,QAAA;UAEA,UAAA9J,IAAA,IAAAC,KAAA,IAAAQ,GAAA,IAAAoL,KAAA,IAAAC,OAAA,IAAAE,OAAA;QACA;QAEA,KAAAjJ,cAAA;UACAC,aAAA,EAAA4I,uBAAA,CAAA5I,aAAA;UACAC,WAAA,EAAA2I,uBAAA,CAAA3I,WAAA;UACAC,OAAA,OAAAzB,aAAA,CAAA+E,aAAA,CAAAtD,OAAA;UACAC,SAAA,OAAA1B,aAAA,CAAA+E,aAAA,CAAArD,SAAA;QACA;;QAEA;QACA,KAAAV,uBAAA;MACA,SAAAuD,KAAA;QACAC,OAAA,CAAAD,KAAA,0CAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA,MAAA7D,EAAA;MACA;QACA,KAAAG,SAAA;MACA;IACA;IAEA;IACA4J,kBAAA;MACA,MAAAvD,SAAA,OAAAjF,IAAA,MAAAX,cAAA,CAAAC,aAAA;MACA,MAAA4F,OAAA,OAAAlF,IAAA,MAAAX,cAAA,CAAAE,WAAA;MAEA,IAAA0F,SAAA,IAAAC,OAAA;QACA,KAAA7C,QAAA,CAAAC,KAAA,MAAA7D,EAAA;QACA;MACA;MAEA;IACA;IAEA;IACA,MAAAgK,4BAAA;MACA,UAAApJ,cAAA,CAAAC,aAAA,UAAAD,cAAA,CAAAE,WAAA;QACA,KAAAH,6BAAA;QACA;MACA;;MAEA;MACA,SAAAC,cAAA,CAAAC,aAAA,SAAAD,cAAA,CAAAE,WAAA;QACA,KAAAN,kBAAA;QACA,KAAAG,6BAAA;QACA;MACA;MAEA;QACA,MAAAsJ,WAAA,QAAA3K,aAAA,CAAA+E,aAAA,CAAA4F,WAAA;QACA,MAAAC,SAAA,QAAAtJ,cAAA,CAAAC,aAAA;QACA,MAAAsJ,OAAA,QAAAvJ,cAAA,CAAAE,WAAA;;QAEA;QACA,MAAAsJ,SAAA,QAAA9K,aAAA,CAAA+K,EAAA;QACA,MAAAnH,MAAA;UACAC,UAAA,EAAA+G,SAAA;UACA9G,QAAA,EAAA+G;QACA;;QAEA;QACA,IAAAC,SAAA,YAAAA,SAAA,KAAAE,SAAA;UACApH,MAAA,CAAAqH,sBAAA,GAAAH,SAAA;QACA;QAIA,MAAA9G,QAAA,cAAAC,KAAA,CAAAC,GAAA,mBAAAyG,WAAA;UAAA/G;QAAA;;QAEA;QACA,IAAAI,QAAA,CAAApH,IAAA,CAAAsO,mBAAA;UACA;UACA1G,OAAA,CAAA2G,GAAA,eAAAnH,QAAA,CAAApH,IAAA,CAAAwO,SAAA;UACA,KAAAlK,kBAAA,GAAA8C,QAAA,CAAApH,IAAA,CAAAwO,SAAA,CAAAC,QAAA;QACA;UACA;UACA7G,OAAA,CAAA2G,GAAA,aAAAnH,QAAA,CAAApH,IAAA,CAAAwO,SAAA;UACA,KAAAlK,kBAAA,GAAA8C,QAAA,CAAApH,IAAA,CAAAwO,SAAA,CAAAC,QAAA;QACA;;QAEA;QACA,SAAAnK,kBAAA;UACAsD,OAAA,CAAA2G,GAAA,aAAAnH,QAAA,CAAApH,IAAA,CAAAwO,SAAA;;UAEA;UACA,KAAAhK,6BAAA,GAAA4C,QAAA,CAAApH,IAAA,CAAA0O,wBAAA;;UAEA;UACA,IAAAtH,QAAA,CAAApH,IAAA,CAAA2O,kBAAA,IAAAvH,QAAA,CAAApH,IAAA,CAAA4O,gBAAA;YACA,KAAArK,qBAAA,sBAAA6C,QAAA,CAAApH,IAAA,CAAA4O,gBAAA;UACA;YACA,KAAArK,qBAAA;UACA;QACA;UACAqD,OAAA,CAAA2G,GAAA;UACA,KAAAhK,qBAAA;UACA,KAAAC,6BAAA;QACA;QAEA,KAAAC,6BAAA;MACA,SAAAkD,KAAA;QACAC,OAAA,CAAAD,KAAA,kCAAAA,KAAA;QACA,KAAArD,kBAAA;QACA,KAAAG,6BAAA;QACA,KAAAD,6BAAA;QACA,KAAAkD,QAAA,CAAAC,KAAA,MAAA7D,EAAA;MACA;IACA;IAEA;IACA,MAAA+K,iBAAA;MACA;QACA;QACA,WAAA1I,KAAA,CAAA2I,aAAA,CAAA7B,QAAA;;QAEA;QACA,UAAAY,iBAAA;;QAEA;QACA,SAAAvJ,kBAAA;UACA,KAAAoD,QAAA,CAAAC,KAAA,MAAA7D,EAAA;UACA;QACA;QAEA,KAAAO,oBAAA;;QAEA;QACA,MAAA0K,UAAA;UACAC,cAAA,OAAAtK,cAAA,CAAAC,aAAA;UACAsK,YAAA,OAAAvK,cAAA,CAAAE,WAAA;UACAC,OAAA,OAAAH,cAAA,CAAAG,OAAA,IAAAuJ,SAAA;UACAc,UAAA,OAAAxK,cAAA,CAAAI,SAAA,IAAAsJ,SAAA;UACAe,IAAA,OAAAtO,KAAA,CAAAD;QACA;;QAEA;QACA,MAAAwG,QAAA,SAAAxH,cAAA,CAAAwP,iBAAA,CACA,KAAAhM,aAAA,CAAA+E,aAAA,CAAAzE,eAAA,EACAqL,UAAA,EACA,KAAA3L,aAAA,CAAA+E,aAAA,CAAAE,iBAAA;QACA;QAEA,IAAAjB,QAAA,CAAApH,IAAA,IAAAoH,QAAA,CAAApH,IAAA,CAAAuH,OAAA;UACA,KAAAG,QAAA,CAAAH,OAAA,MAAAzD,EAAA;UACA,KAAAM,uBAAA;;UAEA;UACA,KAAAjB,aAAA;;UAEA;UACA,KAAA0C,UAAA;QACA;UACA,MAAAwJ,YAAA,GAAAjI,QAAA,CAAApH,IAAA,IAAAoH,QAAA,CAAApH,IAAA,CAAA6D,OAAA,GAAAuD,QAAA,CAAApH,IAAA,CAAA6D,OAAA,QAAAC,EAAA;UACA,KAAA4D,QAAA,CAAAC,KAAA,CAAA0H,YAAA;QACA;MACA,SAAA1H,KAAA;QACAC,OAAA,CAAAD,KAAA,kCAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA,MAAA7D,EAAA;MACA;QACA,KAAAO,oBAAA;MACA;IACA;IAEA;IACA,MAAAiL,kBAAA;MACA;QACA;QACA,WAAAnJ,KAAA,CAAA1C,UAAA,CAAAwJ,QAAA;;QAEA;QACA,SAAAxJ,UAAA,CAAAC,eAAA,UAAAN,aAAA,CAAA+E,aAAA,CAAAzE,eAAA;UACA,KAAAgE,QAAA,CAAAC,KAAA,MAAA7D,EAAA;UACA;QACA;QAEA,KAAAN,UAAA;;QAEA;QACA,MAAAxD,IAAA;UACAsI,kBAAA,OAAAlF,aAAA,CAAA+E,aAAA,CAAAE,iBAAA;UACA8G,IAAA,OAAAtO,KAAA,CAAAD;QACA;;QAEA;QACA,MAAAwG,QAAA,SAAAxH,cAAA,CAAA0P,iBAAA,MAAAlM,aAAA,CAAA+E,aAAA,CAAAzE,eAAA,EAAA1D,IAAA;QAEA,IAAAoH,QAAA,CAAApH,IAAA,CAAAuH,OAAA;UACA,KAAAG,QAAA,CAAAH,OAAA,MAAAzD,EAAA;UACA,KAAAP,mBAAA;;UAEA;UACA,KAAAJ,aAAA;;UAEA;UACA,KAAA0C,UAAA;QACA;UACA,KAAA6B,QAAA,CAAAC,KAAA,CAAAP,QAAA,CAAApH,IAAA,CAAA6D,OAAA,SAAAC,EAAA;QACA;MACA,SAAA6D,KAAA;QACAC,OAAA,CAAAD,KAAA,kCAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA,MAAA7D,EAAA;MACA;QACA,KAAAN,UAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}